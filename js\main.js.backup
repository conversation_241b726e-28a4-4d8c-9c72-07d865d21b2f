const Utils = require('./utils/Utils.js')
const ResourceLoader = require('./utils/ResourceLoader.js')
const EventManager = require('./utils/EventManager.js')
const ImageGenerator = require('./utils/ImageGenerator.js')
const CartoonImageLoader = require('./utils/CartoonImageLoader.js')
const AnimationManager = require('./utils/AnimationManager.js')
const ErrorHandler = require('./utils/ErrorHandler.js')
const AudioManager = require('./utils/AudioManager.js')
const GameSaveManager = require('./utils/GameSaveManager.js')
const UserManager = require('./utils/UserManager.js')
const NetworkManager = require('./utils/NetworkManager.js')
const Board = require('./game/Board.js')
const Slot = require('./game/Slot.js')
const Button = require('./ui/Button.js')
const ParticleSystem = require('./effects/ParticleSystem.js')
const WinScreen = require('./ui/WinScreen.js')
const CollapsiblePanel = require('./ui/CollapsiblePanel.js')
const SettingsButton = require('./ui/SettingsButton.js')
const SettingsPanel = require('./ui/SettingsPanel.js')
const ShareDialog = require('./ui/ShareDialog.js')
const logger = require('./utils/AsyncLogger')

// 新增的管理器模块
const RenderManager = require('./managers/RenderManager.js')
const TouchEventHandler = require('./managers/TouchEventHandler.js')
const PowerUpManager = require('./managers/PowerUpManager.js')
const ProgressManager = require('./managers/ProgressManager.js')
const WechatIntegration = require('./managers/WechatIntegration.js')


// 全局DEBUG配置 - 设为false关闭所有调试日志
const DEBUG_MODE = false

// 添加调试日志 - 现在受DEBUG_MODE控制
if (DEBUG_MODE) {
  logger.info('📦 主模块依赖加载情况:')
  logger.info('- Utils:', typeof Utils)
  logger.info('- ResourceLoader:', typeof ResourceLoader)
  logger.info('- EventManager:', typeof EventManager)
  logger.info('- ImageGenerator:', typeof ImageGenerator)
  logger.info('- CartoonImageLoader:', typeof CartoonImageLoader)
  logger.info('- AnimationManager:', typeof AnimationManager)
  logger.info('- ErrorHandler:', typeof ErrorHandler)
  logger.info('- AudioManager:', typeof AudioManager)
  logger.info('- GameSaveManager:', typeof GameSaveManager)
  logger.info('- UserManager:', typeof UserManager)
  logger.info('- NetworkManager:', typeof NetworkManager)
  logger.info('- Board:', typeof Board)
  logger.info('- Slot:', typeof Slot)
  logger.info('- Button:', typeof Button)
  logger.info('- CollapsiblePanel:', typeof CollapsiblePanel)
  logger.info('- SettingsButton:', typeof SettingsButton)
  logger.info('- SettingsPanel:', typeof SettingsPanel)
}

/**
 * 游戏主控制器
 * 管理游戏的整体流程、Canvas渲染和用户交互
 */
class Main {
  constructor() {
    this.canvas = null
    this.ctx = null
    this.resources = new ResourceLoader()
    this.eventManager = new EventManager()
    this.animationManager = new AnimationManager()
    this.errorHandler = new ErrorHandler()
    this.audioManager = new AudioManager()
    this.userManager = new UserManager()
    this.saveManager = new GameSaveManager()

    // 初始化管理器模块
    this.renderManager = new RenderManager(this)
    this.touchEventHandler = new TouchEventHandler(this)
    this.powerUpManager = new PowerUpManager(this)
    this.progressManager = new ProgressManager(this)
    this.wechatIntegration = new WechatIntegration(this)

    // 游戏状态
    this.gameState = 'loading' // loading, menu, playing, paused, win, lose
    this.currentLevel = 1
    this.score = 0
    this.moves = 0
    this.startTime = 0
    this.endTime = 0
    
    // 游戏对象
    this.board = null
    this.slot = null
    this.powerUpButtons = [] // 道具按钮
    this.fileInfoPanel = null // 文件信息面板
    this.settingsButton = null // 设置按钮
    this.settingsPanel = null // 设置面板
    this.shareDialog = null // 分享对话框
    
    // 道具使用次数
    this.powerUps = {
      undo: 3,      // 撤回次数 
      shuffle: 2    // 洗牌次数
    }
    this.moveHistory = [] // 移动历史，用于撤回
    
    // 交互状态
    this.isInteractionEnabled = true
    this.lastTouchTime = 0
    this.touchDebounceTime = 100 // 防止重复点击
    
    // 调试模式 - 关闭调试模式以提升性能
    this.debugMode = false
    
    // 调试手势检测
    this.debugGestureClicks = []
    this.debugGestureTimeout = null
    
    // 屏幕适配 - 在setupCanvas中初始化
    this.canvasSize = null
    
    // 服务器进度数据
    this.serverProgress = null
    this.isLoadingServerProgress = false
    
    this.init()
  }

  /**
   * 初始化游戏
   */
  async init() {
    if (DEBUG_MODE) logger.info('初始化游戏...')
    
    try {
      // 创建Canvas
      this.setupCanvas()
      
      // 设置触摸事件
      this.touchEventHandler.setupTouchEvents()
      
      // 加载资源
      await this.loadResources()
      
      // 初始化音频系统
      await this.audioManager.init()
      
      // 初始化用户系统
      await this.initUserSystem()
      
      // 加载游戏进度
      this.loadGameProgress()
      
      // 初始化游戏对象
      this.initGameObjects()
      
      // 开始游戏循环
      this.startGameLoop()
      
      // 设置微信小游戏生命周期监听
      this.wechatIntegration.setupWechatLifeCycle()

      // 立即启用分享菜单（确保在游戏加载完成前就可用）
      this.wechatIntegration.enableShareMenu()
      
      // 根据游戏进度决定初始界面
      this.setInitialState()
      
      if (DEBUG_MODE) logger.info('游戏初始化完成！')
    } catch (error) {
      logger.error('游戏初始化失败:', error)
      this.showError('游戏初始化失败，请重试')
    }
  }

  /**
   * 设置Canvas
   */
  setupCanvas() {
    this.canvas = wx.createCanvas()
    this.ctx = this.canvas.getContext('2d')
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    const pixelRatio = systemInfo.pixelRatio
    const screenWidth = systemInfo.screenWidth
    const screenHeight = systemInfo.screenHeight
    
    // 设置Canvas物理尺寸（用于实际渲染）
    this.canvas.width = screenWidth * pixelRatio
    this.canvas.height = screenHeight * pixelRatio
    
    // 设置Canvas逻辑尺寸（用于坐标计算）
    this.canvas.style = {
      width: screenWidth + 'px',
      height: screenHeight + 'px'
    }
    
    // 缩放Canvas以适应设备像素比
    this.ctx.scale(pixelRatio, pixelRatio)
    
    // 更新canvasSize对象
    this.canvasSize = {
      width: this.canvas.width,
      height: this.canvas.height,
      pixelRatio: pixelRatio,
      screenWidth: screenWidth,
      screenHeight: screenHeight
    }
    
    if (DEBUG_MODE) {
      logger.info(`Canvas设置完成: 物理尺寸${this.canvas.width}x${this.canvas.height}, 逻辑尺寸${screenWidth}x${screenHeight}, 像素比${pixelRatio}`)
    }
  }

  /**
   * 加载游戏资源
   */
  async loadResources() {
    if (DEBUG_MODE) logger.info('开始加载资源...')
    
    try {
      // 加载卡通动物图片
      if (DEBUG_MODE) logger.info('🎨 正在加载卡通动物图片...')
      const cartoonImages = await CartoonImageLoader.loadCartoonImages()
      
      // 检查加载结果
      const loadedCount = Object.keys(cartoonImages).length
      if (loadedCount === 0) {
        throw new Error('没有成功加载任何图片文件，请检查images/cartoon_animals/目录中的图片文件')
      }
      
      if (DEBUG_MODE) logger.info(`🎉 成功加载 ${loadedCount}/8 张卡通动物图片`)
      
      // 生成背景图片（简单的池塘背景）
      const bgCanvas = ImageGenerator.createCanvas(400, 600)
      const bgCtx = bgCanvas.getContext('2d')
      
      // 绘制池塘背景
      const gradient = bgCtx.createLinearGradient(0, 0, 0, 600)
      gradient.addColorStop(0, '#87CEEB')  // 天蓝色
      gradient.addColorStop(0.6, '#87CEEB')
      gradient.addColorStop(0.6, '#20B2AA')  // 海蓝色
      gradient.addColorStop(1, '#008B8B')   // 深青色
      
      bgCtx.fillStyle = gradient
      bgCtx.fillRect(0, 0, 400, 600)
      
      // 添加一些云朵装饰
      bgCtx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      for (let i = 0; i < 5; i++) {
        const x = Math.random() * 350
        const y = Math.random() * 150
        bgCtx.beginPath()
        bgCtx.ellipse(x, y, 30 + Math.random() * 20, 15 + Math.random() * 10, 0, 0, 2 * Math.PI)
        bgCtx.fill()
      }
      
      // 使用Map的set方法正确添加资源
      this.resources.images.set('background', bgCanvas)
      
      // 使用从后端API加载的动物图片 - 支持最多14种
      const baseImageMapping = []
      for (let i = 1; i <= 14; i++) {
        baseImageMapping.push({
          key: `animal${i}`,
          blockIndex: i - 1,
          name: `动物${i}`
        })
      }
      
      // 处理基础映射（0-13，最多14种图片）
      let successCount = 0
      const loadedImages = []
      baseImageMapping.forEach(mapping => {
        if (cartoonImages[mapping.key]) {
          logger.info(`✅ 使用API图片: ${mapping.name}`)
          const resizedImage = this.resizeImageToCanvas(cartoonImages[mapping.key], 48)
          this.resources.images.set(`block_${mapping.blockIndex}`, resizedImage)
          loadedImages.push({ image: resizedImage, key: mapping.key, blockIndex: mapping.blockIndex })
          successCount++
        } else {
          logger.warn(`❌ 图片缺失: ${mapping.name} (${mapping.key})`)
        }
      })
      
      // 扩展映射：为类型14-20创建循环映射（支持第20关等高难度关卡）
      if (loadedImages.length > 0) {
        logger.info(`🔄 开始创建扩展图片映射，支持方块类型${loadedImages.length}-20...`)
        for (let blockType = loadedImages.length; blockType <= 20; blockType++) {
          // 循环使用已加载的图片
          const sourceIndex = blockType % loadedImages.length
          const sourceImage = loadedImages[sourceIndex]
          
          // 复制图片到新的方块类型
          this.resources.images.set(`block_${blockType}`, sourceImage.image)
          logger.info(`🔄 方块类型${blockType} -> 复用${sourceImage.key} (原block_${sourceImage.blockIndex})`)
        }
        
        logger.info(`🎯 扩展映射完成：支持方块类型0-20`)
      }
      
      // if (successCount < 5) {
      //   throw new Error(`图片加载不足，只加载了${successCount}张，至少需要5张图片才能正常游戏`)
      // }
      
      logger.info(`🎮 游戏图片准备完成：${successCount}/14 张基础图片`)
      logger.info(`🎮 实际加载了：${successCount} 种不同的动物图片`)
      logger.info(`🎮 总计支持方块类型：0-20 (21种类型，循环使用${successCount}种图片)`)
      
      // 调试：输出所有已加载的图片资源
      logger.info(`📋 已加载的图片资源列表:`)
      for (let i = 0; i <= 20; i++) {
        const hasImage = this.resources.images.has(`block_${i}`)
        logger.info(`  block_${i}: ${hasImage ? '✅' : '❌'}`)
      }
      
      logger.info('资源加载完成！')
    } catch (error) {
      logger.error('资源加载失败:', error)
      throw error
    }
  }

  /**
   * 将图片调整到指定尺寸的Canvas
   * @param {HTMLImageElement} img - 原始图片
   * @param {number} size - 目标尺寸
   * @returns {HTMLCanvasElement}
   */
  resizeImageToCanvas(img, size) {
    const canvas = ImageGenerator.createCanvas(size, size)
    const ctx = canvas.getContext('2d')
    
    // 清空画布
    ctx.clearRect(0, 0, size, size)
    
    // 绘制调整尺寸后的图片
    ctx.drawImage(img, 0, 0, size, size)
    
    return canvas
  }

  /**
   * 初始化用户系统
   */
  async initUserSystem() {
    logger.info('👤 初始化用户系统...')
    try {
      // 初始化用户管理器
      await this.userManager.init()
      
      // 将用户管理器关联到存档管理器
      this.saveManager.setUserManager(this.userManager)
      
      logger.info('👤 用户系统初始化完成')
    } catch (error) {
      logger.error('❌ 用户系统初始化失败:', error)
      // 即使用户系统失败，游戏仍然可以运行（使用默认存档）
    }
  }

  /**
   * 加载游戏进度
   */
  loadGameProgress() {
    logger.info('📱 加载游戏进度...')
    try {
      const saveData = this.saveManager.getSaveData()
      logger.info('📱 存档数据:', saveData)
      
          // 设置当前关卡为已解锁的关卡
    this.currentLevel = saveData.currentLevel
    
    // 设置全局变量用于调试（Block.js会用到）
    if (typeof window !== 'undefined') {
      window.currentLevel = this.currentLevel
    }
      
      // 记录是否首次游戏
      this.isFirstPlay = saveData.firstPlay
      
      logger.info(`📱 游戏进度加载完成: 当前关卡 ${this.currentLevel}, 首次游戏: ${this.isFirstPlay}`)
    } catch (error) {
      logger.error('❌ 加载游戏进度失败:', error)
      // 使用默认值
      this.currentLevel = 1
      this.isFirstPlay = true
    }
  }

  /**
   * 初始化游戏对象
   */
  initGameObjects() {
    const screenWidth = this.canvasSize.screenWidth
    const screenHeight = this.canvasSize.screenHeight
    
    logger.info(`屏幕尺寸: ${screenWidth}x${screenHeight}`)
    
    // 初始化游戏板
    const boardWidth = screenWidth - 40
    const boardHeight = screenHeight * 0.45  // 稍微减小游戏板高度，为更多UI元素留出空间
    const boardX = 20
    const boardY = 150  // 从80增加到130，确保与扩大的标签栏(100px)有足够间距

    logger.info(`游戏板布局: 位置(${boardX}, ${boardY}), 尺寸${boardWidth}x${boardHeight}`)

    this.board = new Board(boardX, boardY, boardWidth, boardHeight)
    
    // 初始化槽位 - 美化版本，稍微增大
    const slotWidth = screenWidth - 30  // 减少左右边距，让槽位更宽
    const slotHeight = 80  // 增加槽位高度，让方块更大更美观
    const slotX = 15  // 减少左边距
    const slotY = screenHeight - slotHeight - 75 // 为道具按钮留出空间，稍微调整位置
    
    logger.info(`槽位布局: 位置(${slotX}, ${slotY}), 尺寸${slotWidth}x${slotHeight}`)
    
    this.slot = new Slot(slotX, slotY, slotWidth, slotHeight)
    
    // 设置槽位消除回调
    this.slot.setEliminationCallback((type, count) => {
      this.onBlockEliminated(type, count)
    })
    
    // 设置槽位消除完成回调
    this.slot.setEliminationCompleteCallback(() => {
      this.onEliminationComplete()
    })
    
    // 设置槽位动画管理器
    this.slot.setAnimationManager(this.animationManager)
    
    // 设置槽位音频管理器
    this.slot.setAudioManager(this.audioManager)
    
    // 初始化道具按钮
    this.initPowerUpButtons()
    
    // 初始化文件信息面板
    this.initFileInfoPanel()
    
    // 初始化设置按钮和设置面板
    this.initSettingsUI()
    
    // 初始化分享对话框
    this.initShareDialog()

    // 初始化粒子系统和胜利页面
    this.particleSystem = new ParticleSystem()
    this.winScreen = new WinScreen(this.canvasSize, this.animationManager, this.particleSystem)

    logger.info('游戏对象初始化完成')
  }

  /**
   * 初始化道具按钮
   */
  initPowerUpButtons() {
    const screenWidth = this.canvasSize.screenWidth
    const screenHeight = this.canvasSize.screenHeight
    const buttonWidth = 80
    const buttonHeight = 40
    const buttonSpacing = 10
    // 只有两个按钮，重新计算居中位置
    const startX = (screenWidth - (buttonWidth * 2 + buttonSpacing)) / 2
    const buttonY = screenHeight - 60

    // 撤回按钮
    const undoButton = new Button(
      startX, buttonY, buttonWidth, buttonHeight,
      `撤回(${this.powerUps.undo})`,
      () => this.powerUpManager.usePowerUp('undo')
    )
    undoButton.setTheme('blue')
    this.powerUpButtons.push(undoButton)

    // 洗牌按钮
    const shuffleButton = new Button(
      startX + buttonWidth + buttonSpacing, buttonY, buttonWidth, buttonHeight,
      `洗牌(${this.powerUps.shuffle})`,
      () => this.powerUpManager.usePowerUp('shuffle')
    )
    shuffleButton.setTheme('orange')
    this.powerUpButtons.push(shuffleButton)
  }

  /**
   * 初始化文件信息面板
   * 用于显示游戏调试信息和文件状态
   */
  initFileInfoPanel() {
    const screenWidth = this.canvasSize.screenWidth
    const panelWidth = Math.min(300, screenWidth - 40)
    const panelX = 20
    const panelY = 80

    // 创建文件信息面板
    this.fileInfoPanel = new CollapsiblePanel(
      panelX, panelY, panelWidth,
      '游戏信息', // 面板标题
      [], // 初始内容为空
      true // 初始状态为折叠
    )

    // 设置面板主题
    this.fileInfoPanel.setTheme('info')

    // 更新面板内容
    this.updateFileInfoPanel()

    logger.info('文件信息面板初始化完成')
  }

  /**
   * 初始化设置UI组件
   */
  initSettingsUI() {
    const screenWidth = this.canvasSize.screenWidth
    const screenHeight = this.canvasSize.screenHeight
    
    // 创建设置按钮（左上角，避免与步数显示重叠）
    const buttonSize = 35  // 保持35px尺寸
    this.settingsButton = new SettingsButton(
      25, // 左边距15px
      56, // 顶部边距80px，位于步数显示下方，避免重叠
      buttonSize,
      () => this.showSettingsPanel()
    )
    
    // 创建设置面板（居中）
    const panelWidth = Math.min(300, screenWidth - 80)
    const panelHeight = 520 // 增加到520，确保所有按钮都能完整显示
    const panelX = (screenWidth - panelWidth) / 2
    const panelY = (screenHeight - panelHeight) / 2
    
    this.settingsPanel = new SettingsPanel(
      panelX, panelY, panelWidth, panelHeight,
      {
        onShow: () => this.audioManager.playSound('click'),
        onHide: () => this.audioManager.playSound('click'),
        onSoundToggle: (enabled) => this.toggleSound(enabled),
        onMusicToggle: (enabled) => this.toggleMusic(enabled),
        onRestartLevel: () => this.restartCurrentLevel(),
        onBackToLevels: () => this.backToLevelSelect(),
        onFeedbackSuccess: () => this.showMessage('感谢您的反馈！我们会认真对待您的建议。', 3000),
        onFeedbackError: (error) => this.showMessage(error, 3000),
        onFeedbackInputFocus: (currentText, callback) => this.handleFeedbackInput(currentText, callback),
        getUserInfo: () => this.userManager.getUserInfo()
      }
    )
    
    // 同步当前音频设置状态
    this.settingsPanel.setSoundEnabled(this.audioManager.getSoundEnabled())
    this.settingsPanel.setMusicEnabled(this.audioManager.getMusicEnabled())
    
    logger.info('设置UI组件初始化完成')
  }

  /**
   * 初始化分享对话框
   */
  initShareDialog() {
    const screenWidth = this.canvasSize.screenWidth
    const screenHeight = this.canvasSize.screenHeight
    
    // 创建分享对话框（居中）
    const dialogWidth = Math.min(300, screenWidth - 80)
    const dialogHeight = 250
    const dialogX = (screenWidth - dialogWidth) / 2
    const dialogY = (screenHeight - dialogHeight) / 2
    
    // 初始化为null，需要时动态创建
    this.shareDialog = null
    
    logger.info('分享对话框配置初始化完成')
  }

  /**
   * 显示分享对话框
   * @param {string} powerUpType - 道具类型 ('undo' 或 'shuffle')
   */
  showShareDialog(powerUpType) {
    const screenWidth = this.canvasSize.screenWidth
    const screenHeight = this.canvasSize.screenHeight
    
    // 创建分享对话框
    const dialogWidth = Math.min(300, screenWidth - 80)
    const dialogHeight = 250
    const dialogX = (screenWidth - dialogWidth) / 2
    const dialogY = (screenHeight - dialogHeight) / 2
    
    this.shareDialog = new ShareDialog(dialogX, dialogY, dialogWidth, dialogHeight, powerUpType)
    this.shareDialog.show()
    
    // 禁用游戏交互
    this.isInteractionEnabled = false
  }

  /**
   * 处理分享确认
   */
  handleShareConfirm() {
    logger.info('🎯 用户确认分享')
    
    // 隐藏对话框
    if (this.shareDialog) {
      const powerUpType = this.shareDialog.powerUpType
      this.shareDialog.hide()
      
      // 直接调用转发给好友功能
      if (typeof wx !== 'undefined') {
        logger.info('🚀 直接启动转发给好友界面')
        this.shareToFriendDirect(powerUpType)
      } else {
        // 非微信环境，直接增加次数（用于测试）
        logger.info('⚠️ 非微信环境，模拟分享成功')
        this.powerUps[powerUpType]++
        this.powerUpManager.updatePowerUpButtons()
        this.showMessage(`测试模式：${powerUpType === 'undo' ? '撤回' : '洗牌'}次数+1`, 2000)
        
        // 恢复游戏交互
        this.isInteractionEnabled = true
      }
    }
  }

  /**
   * 直接转发给好友（立即给奖励）
   * @param {string} powerUpType - 道具类型
   */
  shareToFriendDirect(powerUpType) {
    if (!wx.shareAppMessage) {
      this.showMessage('当前版本不支持转发功能', 2000)
      this.isInteractionEnabled = true
      return
    }

    logger.info(`🚀 开始直接转发分享，道具类型: ${powerUpType}`)

    // 先立即给予奖励
    this.onShareSuccess(powerUpType, '转发给好友')

    // 创建分享内容
    const shareData = {
      title: '鸭了个鸭呀 - 超好玩的消除游戏！',
      desc: `我已经玩到第${this.currentLevel}关了，快来一起挑战吧！`,
      query: `from=friend&level=${this.currentLevel}&score=${this.score}`,
      success: () => {
        logger.info('✅ 用户完成了转发操作')
      },
      fail: (err) => {
        logger.info('ℹ️ 用户取消了转发或转发失败:', err)
        // 不做任何处理，因为奖励已经给了
      }
    }

    try {
      wx.shareAppMessage(shareData)
    } catch (error) {
      logger.error('❌ 调用转发API异常:', error)
      // 即使API调用失败，奖励已经给了，不需要回滚
    }
  }


  /**
   * 转发给好友
   * @param {string} powerUpType - 道具类型
   */
  shareToFriend(powerUpType) {
    if (!wx.shareAppMessage) {
      this.showMessage('当前版本不支持转发功能', 2000)
      this.isInteractionEnabled = true
      return
    }

    logger.info(`🔄 开始转发分享，道具类型: ${powerUpType}`)

    // 创建分享内容
    const shareData = {
      title: '鸭了个鸭呀 - 超好玩的消除游戏！',
      desc: `我已经玩到第${this.currentLevel}关了，快来一起挑战吧！`,
      // 小游戏不需要path参数，微信会自动处理
      success: () => {
        logger.info('✅ 微信API报告转发成功')
        this.onShareSuccess(powerUpType, '转发')
      },
      fail: (err) => {
        logger.error('❌ 微信API报告转发失败:', err)
        this.showMessage('转发失败，请重试', 2000)
        this.isInteractionEnabled = true
      }
    }

    // 如果有自定义图片，可以添加imageUrl
    // shareData.imageUrl = 'path/to/share-image.jpg'

    try {
      wx.shareAppMessage(shareData)
      
      // 微信小游戏的success回调可能不可靠，使用延时处理
      // 假设用户在3秒内没有取消就认为分享成功
      logger.info('⏰ 设置分享超时检测')
      setTimeout(() => {
        // 检查交互是否仍被禁用（说明success/fail都没被调用）
        if (!this.isInteractionEnabled) {
          logger.info('⚠️ 微信分享回调未触发，假设分享成功')
          this.onShareSuccess(powerUpType, '转发')
        }
      }, 3000)
      
    } catch (error) {
      logger.error('❌ 调用微信分享API失败:', error)
      this.showMessage('分享功能不可用', 2000)
      this.isInteractionEnabled = true
    }
  }


  /**
   * 转发给好友（不给奖励，仅调用API）
   * @param {string} powerUpType - 道具类型
   */
  shareToFriendWithoutReward(powerUpType) {
    if (!wx.shareAppMessage) {
      logger.warn('微信分享API不可用')
      return
    }

    logger.info(`📤 调用微信分享API，道具类型: ${powerUpType}`)

    // 创建分享内容
    const shareData = {
      title: '鸭了个鸭呀 - 超好玩的消除游戏！',
      desc: `我已经玩到第${this.currentLevel}关了，快来一起挑战吧！`,
      success: () => {
        logger.info('✅ 微信API确认分享成功')
      },
      fail: (err) => {
        logger.error('❌ 微信API报告分享失败:', err)
      }
    }

    try {
      wx.shareAppMessage(shareData)
    } catch (error) {
      logger.error('❌ 调用微信分享API异常:', error)
    }
  }





  /**
   * 分享成功处理
   * @param {string} powerUpType - 道具类型
   * @param {string} shareType - 分享方式
   */
  onShareSuccess(powerUpType, shareType) {
    // 防止重复调用
    if (this.isInteractionEnabled) {
      logger.warn(`⚠️ 分享成功回调重复调用，忽略: ${shareType}`)
      return
    }
    
    logger.info(`🎉 分享成功: ${shareType}, 道具类型: ${powerUpType}`)
    
    // 分享成功后增加道具次数
    const oldCount = this.powerUps[powerUpType]
    this.powerUps[powerUpType]++
    const newCount = this.powerUps[powerUpType]
    
    logger.info(`📈 道具次数更新: ${powerUpType} ${oldCount} -> ${newCount}`)
    
    // 更新道具按钮显示
    this.powerUpManager.updatePowerUpButtons()
    
    // 显示成功消息
    const powerUpName = powerUpType === 'undo' ? '撤回' : '洗牌'
    this.showMessage(`${shareType}成功！${powerUpName}次数+1`, 2000)
    
    // 恢复游戏交互
    this.isInteractionEnabled = true
    
    // 可选：播放成功音效
    if (this.audioManager) {
      this.audioManager.playSound('powerup')
    }
  }

  /**
   * 显示设置面板
   */
  showSettingsPanel() {
    if (this.settingsPanel) {
      this.settingsPanel.show()
    }
  }

  /**
   * 切换音效开关
   * @param {boolean} enabled - 是否启用音效
   */
  toggleSound(enabled) {
    this.audioManager.setSoundEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`音效${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 切换背景音乐开关
   * @param {boolean} enabled - 是否启用背景音乐
   */
  toggleMusic(enabled) {
    this.audioManager.setMusicEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`背景音乐${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 返回关卡选择
   */
  backToLevelSelect() {
    logger.info('返回关卡选择页面')
    this.setState('levelSelect')
  }

  /**
   * 处理意见反馈输入（小程序环境）
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 对话框点击回调（用于开发者模式触发）
   */
  handleFeedbackInput(currentText, callback, clickCallback) {
    // 创建自定义反馈对话框，支持点击计数
    this.showCustomFeedbackDialog(currentText, callback, clickCallback)
  }

  /**
   * 显示自定义反馈对话框
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 点击计数回调
   */
  showCustomFeedbackDialog(currentText, callback) {
    // 如果是微信小程序环境，使用prompt输入
    if (typeof wx !== 'undefined' && wx.showModal) {
      wx.showModal({
        title: '意见反馈',
        editable: true,
        placeholderText: currentText || '请输入您的意见和建议...',
        success: (res) => {
          if (res.confirm) {
            callback(res.content || currentText || '')
          } else {
            callback(currentText || '')
          }
        }
      })
    } else {
      // Web环境，使用prompt作为fallback
      const input = prompt('请输入您的意见和建议:', currentText || '')
      if (input !== null) {
        callback(input)
      } else {
        callback(currentText || '')
      }
    }
  }

  /**
   * 重新开始当前关卡
   */
  restartCurrentLevel() {
    logger.info(`🔄 重新开始关卡 ${this.currentLevel}`)
    
    // 重新开始当前关卡，保持关卡编号不变
    this.startLevel(this.currentLevel)
    
    // 显示提示信息
    this.showMessage(`重新开始第${this.currentLevel}关`, 2000)
  }

  /**
   * 更新文件信息面板内容
   */
  updateFileInfoPanel() {
    if (!this.fileInfoPanel) return

    const content = [
      `游戏版本: v1.0.0`,
      `当前关卡: ${this.currentLevel}`,
      `移动次数: ${this.moves}`,
      `游戏状态: ${this.gameState}`,
      `道具剩余: 撤回${this.powerUps.undo} 洗牌${this.powerUps.shuffle}`,
      `屏幕尺寸: ${this.canvasSize.screenWidth}x${this.canvasSize.screenHeight}`,
      `像素比: ${this.canvasSize.pixelRatio}`,
      `调试模式: ${this.debugMode ? '开启' : '关闭'}`
    ]

    this.fileInfoPanel.setContent(content)
  }

  /**
   * 处理方块点击
   * @param {Block} block - 被点击的方块
   */
  handleBlockClick(block) {
    logger.info(`点击了类型为 ${block.type} 的方块`);
    
    this.audioManager.playSound('click');
    
    const moveRecord = {
      block: block,
      originalX: block.x,
      originalY: block.y,
      originalLayer: block.layer,
      originalWidth: block.initialWidth,
      originalHeight: block.initialHeight,
      timestamp: Date.now()
    };
    
    block.playClickAnimation(this.animationManager);
    this.moves++;
    
    // 将方块添加到槽位
    this.slot.addBlock(block);

    // 从棋盘上移除方块
    this.board.removeBlock(block);
    this.moveHistory.push(moveRecord);
    if (this.moveHistory.length > 10) {
      this.moveHistory.shift();
    }
    this.powerUpManager.updatePowerUpButtons();

    // 检查游戏失败条件
    if (this.slot.getBlockCount() > this.slot.maxBlocks) {
      // 槽位溢出，立即失败
      logger.info('槽位溢出（超过最大容量），游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else if (this.slot.isFull()) {
      // 槽位已满，等待动画完成再判定
      logger.info('槽位已满，等待动画完成后再判定游戏结束');
      this.waitForAnimationsAndCheckFailure(block);
    } else {
      // 其他情况，等待可能的消除动画完成后再检查游戏状态
      this.delayedCheckGameState();
    }
  }

  /**
   * 等待方块移动和消除动画完成后检查槽位状态
   * @param {Block} block - 最近放入槽位的方块
   */
  waitForAnimationsAndCheckFailure(block) {
    logger.info('等待方块移动和消除动画完成后检查槽位状态');
    const checkFn = () => {
      if (block.isMoving || this.slot.isEliminationInProgress()) {
        requestAnimationFrame(checkFn);
      } else {
        // 动画完成后先检查消除结果
        if (this.slot.isFull()) {
          logger.info('动画完成后槽位仍满，游戏失败');
          this.endTime = Date.now();
          this.audioManager.playSound('lose');
          this.setState('lose');
        } else {
          logger.info('动画完成后槽位未满，继续游戏');
          this.delayedCheckGameState();
        }
      }
    };
    checkFn();
  }

  /**
   * 方块消除回调
   * @param {number} type - 消除的方块类型
   * @param {number} count - 消除的数量
   */
  onBlockEliminated(type, count) {
    // 播放消除音效
    this.audioManager.playSound('eliminate')
    
    // 增加分数
    this.score += count * 100
    
    logger.info(`消除了 ${count} 个类型 ${type} 的方块，得分: ${count * 100}`)
  }

  /**
   * 消除动画完成回调
   * 在消除动画结束后重新检查游戏状态
   */
  onEliminationComplete() {
    if (DEBUG_MODE) logger.info('🎬 消除动画完成，重新检查游戏状态')

    // 在消除动画完成后，重新检查游戏状态
    if (this.slot.isFull()) {
      // 如果槽位仍然是满的，说明无法消除，游戏失败
      logger.info('消除动画完成后槽位仍满，游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else {
      // 检查是否胜利或其他游戏状态
      if (DEBUG_MODE) logger.info('🎯 消除动画完成，现在检查游戏胜利状态');
      this.checkGameState();
    }
  }

  /**
   * 延迟检查游戏状态（等待可能的消除动画）
   */
  delayedCheckGameState() {
    if (DEBUG_MODE) logger.info('⏰ 延迟检查游戏状态开始');

    // 如果槽位正在进行消除动画，不立即检查游戏状态
    if (this.slot.isEliminationInProgress()) {
      if (DEBUG_MODE) logger.info('🎬 槽位正在消除动画中，等待动画完成后再检查游戏状态');
      return;
    }

    // 给一个短暂的延迟，让消除动画有机会开始
    setTimeout(() => {
      if (this.slot.isEliminationInProgress()) {
        if (DEBUG_MODE) logger.info('🎬 检测到消除动画已开始，等待动画完成');
        return;
      }
      // 如果没有消除动画，立即检查游戏状态
      if (DEBUG_MODE) logger.info('✅ 没有消除动画，立即检查游戏状态');
      this.checkGameState();
    }, 50); // 50ms的短暂延迟
  }

  /**
   * 检查游戏状态
   */
  checkGameState() {
    if (this.board.isWin()) {
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('关卡完成！');
      this.saveLevelCompletion();
      this.audioManager.playSound('win');
      this.setState('win');
    } else if (!this.board.hasClickableBlocks() && !this.slot.isFull()) {
      // 之前这里是 this.slot.isFull() && !this.board.hasClickableBlocks()
      // 现在改为：如果板上没有可点击的了，并且槽还没满（如果满了会在handleBlockClick中处理），这也是一种死局
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('游戏失败（无可用方块且槽未满）！');
      this.audioManager.playSound('lose');
      this.setState('lose');
    }
    // 注意：如果 this.slot.isFull()，已经在 handleBlockClick 中处理了失败
    // 所以这里不再需要检查 this.slot.isFull() 的情况。
    // 保留一个可能性：如果棋盘清空，但槽是满的，board.isWin()可能为true，slot.isFull()也为true。
    // isWin() 应该优先于槽满失败。
    // 当前的 isWin() 是 this.blocks.filter(block => block.isVisible).length === 0 (棋盘上的方块)
    // 这是对的，胜利条件是清空棋盘。
  }

  /**
   * 保存关卡完成记录
   */
  saveLevelCompletion() {
    try {
      const playTime = Math.floor((this.endTime - this.startTime) / 1000) // 转换为秒
      const record = {
        score: this.score,
        moves: this.moves,
        playTime: playTime,
        timestamp: Date.now()
      }
      
      if (DEBUG_MODE) logger.info(`📱 保存关卡 ${this.currentLevel} 完成记录:`, record)
      
      // 使用存档管理器保存关卡完成记录（本地）
      this.saveManager.levelCompleted(this.currentLevel, record)
      
      // 检查是否为开发者模式
      if (this.saveManager.isDeveloperMode()) {
        logger.info('🛠️ 开发者模式：跳过服务器记录同步')
        return
      }
      
      // 如果用户已登录服务器，同时保存到服务器
      if (this.userManager && this.userManager.isConnectedToServer()) {
        logger.info(`🌐 保存关卡${this.currentLevel}记录到服务器...`)
        this.userManager.saveLevelProgress(
          this.currentLevel,
          this.score,
          this.moves,
          playTime
        ).then(result => {
          if (result.success) {
            logger.info(`✅ 关卡${this.currentLevel}记录已同步到服务器`)
            // 如果是最佳记录，显示提示
            if (result.data && result.data.isBestRecord) {
              this.showMessage(`🎉 刷新了关卡${this.currentLevel}的最佳记录！`, 3000)
            }
          } else {
            logger.warn(`⚠️ 关卡${this.currentLevel}记录同步到服务器失败:`, result.error)
          }
        }).catch(error => {
          logger.error(`❌ 关卡${this.currentLevel}记录同步到服务器异常:`, error)
        })
      } else {
        logger.info('🔒 未连接到服务器，仅保存本地记录')
      }
    } catch (error) {
      logger.error('❌ 保存关卡记录失败:', error)
    }
  }

  /**
   * 开始指定关卡
   * @param {number} level - 关卡编号
   */
  startLevel(level) {
    if (DEBUG_MODE) logger.info(`开始关卡 ${level}`)
    
    this.currentLevel = level
    
    // 更新全局变量用于调试（Block.js会用到）
    if (typeof window !== 'undefined') {
      window.currentLevel = level
    }
    
    this.score = 0
    this.moves = 0
    this.startTime = Date.now()
    this.endTime = 0 // 重置结束时间
    this.moveHistory = [] // 清空移动历史
    
    // 重置道具次数（每关重置）
    this.powerUps = {
      undo: 3,
      shuffle: 2
    }
    
    // 清空槽位
    this.slot.clear()
    
    // 初始化游戏板
    this.board.initLevel(level)
    
    // 更新道具按钮
    this.powerUpManager.updatePowerUpButtons()
    
    // 播放背景音乐
    this.audioManager.playBackgroundMusic()
    
    // 设置游戏状态
    this.setState('playing')
  }

  /**
   * 设置游戏状态
   * @param {string} state - 新状态
   */
  setState(state) {
    if (DEBUG_MODE) logger.info(`游戏状态变更: ${this.gameState} -> ${state}`)
    this.gameState = state
    
    // 根据状态进行相应处理
    switch (state) {
      case 'loading':
        this.isInteractionEnabled = false
        break
      case 'menu':
        this.isInteractionEnabled = true
        break
      case 'levelSelect':
        this.isInteractionEnabled = true
        // 进入关卡选择时，尝试加载服务器进度（开发者模式下跳过）
        if (!this.saveManager.isDeveloperMode()) {
          this.progressManager.loadServerProgress()
        } else {
          logger.info('🛠️ 开发者模式：跳过服务器进度加载')
        }
        break
      case 'playing':
        this.isInteractionEnabled = true
        break
      case 'paused':
        this.isInteractionEnabled = false
        break
      case 'win':
        this.isInteractionEnabled = true
        // 显示美化的胜利页面
        if (this.winScreen) {
          // 计算用时，确保不为负数
          let playTime = 0
          if (this.endTime > 0 && this.startTime > 0 && this.endTime >= this.startTime) {
            playTime = Math.floor((this.endTime - this.startTime) / 1000)
          }

          this.winScreen.show({
            time: playTime,
            score: this.score,
            moves: this.moves
          })
        }
        break
      case 'lose':
        this.isInteractionEnabled = true
        break
    }
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    logger.error(message)
    // 这里可以显示错误对话框
  }

  /**
   * 开始游戏循环
   */
  startGameLoop() {
    let lastTime = 0
    
    const gameLoop = (timestamp) => {
      const deltaTime = timestamp - lastTime
      lastTime = timestamp
      
      // 更新游戏状态
      this.update(deltaTime)
      
      // 渲染游戏画面
      this.render()
      
      // 请求下一帧
      requestAnimationFrame(gameLoop)
    }
    
    requestAnimationFrame(gameLoop)
    if (DEBUG_MODE) logger.info('游戏循环已启动')
  }

  /**
   * 更新游戏状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新动画管理器
    this.animationManager.update(Date.now())

    // 更新文件信息面板
    if (this.fileInfoPanel) {
      this.fileInfoPanel.update(deltaTime)
    }

    // 更新设置UI
    if (this.settingsButton) {
      this.settingsButton.update(deltaTime)
    }
    if (this.settingsPanel) {
      this.settingsPanel.update(deltaTime)
    }

    // 更新分享对话框
    if (this.shareDialog) {
      this.shareDialog.update(deltaTime)
    }

    // 更新胜利页面
    if (this.winScreen && this.gameState === 'win') {
      this.winScreen.update(deltaTime)
    }

    if (this.gameState === 'playing') {
      // 更新游戏对象
      if (this.board) {
        this.board.update(deltaTime)
      }
      
      if (this.slot) {
        this.slot.update(deltaTime)
      }
      
      // 更新道具按钮
      this.powerUpButtons.forEach(button => {
        button.update(deltaTime)
      })

      // 定期更新文件信息面板内容
      if (this.fileInfoPanel && Date.now() % 1000 < 50) { // 大约每秒更新一次
        this.updateFileInfoPanel()
      }
    }
  }

  /**
   * 渲染游戏画面
   */
  render() {
    // 使用渲染管理器进行渲染
    this.renderManager.render()
  }









  /**
   * 渲染欢迎信息
   */
  renderWelcomeMessage(ctx, centerX, centerY, time) {
    // 欢迎卡片背景
    const cardWidth = 280
    const cardHeight = 80

    // 卡片阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, centerX - cardWidth / 2 + 3, centerY - cardHeight / 2 + 3, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片渐变背景
    const cardGradient = ctx.createLinearGradient(centerX - cardWidth / 2, centerY - cardHeight / 2, centerX + cardWidth / 2, centerY + cardHeight / 2)
    cardGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)')
    cardGradient.addColorStop(1, 'rgba(240, 248, 255, 0.9)')

    ctx.fillStyle = cardGradient
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片边框
    ctx.strokeStyle = 'rgba(74, 144, 226, 0.3)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.stroke()

    // 欢迎文字
    ctx.fillStyle = '#4CAF50'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('🎉 欢迎来到鸭了个鸭呀！', centerX, centerY - 5)

  }

  /**
   * 渲染增强版开始提示
   */
  renderEnhancedStartPrompt(ctx, centerX, centerY, time) {
    // 脉冲动画效果
    const pulseScale = 1 + Math.sin(time * 3) * 0.1
    const pulseAlpha = 0.7 + Math.sin(time * 3) * 0.3

    ctx.save()
    ctx.globalAlpha = pulseAlpha
    ctx.translate(centerX, centerY)
    ctx.scale(pulseScale, pulseScale)

    // 提示背景
    const promptWidth = 260
    const promptHeight = 50

    // 渐变背景
    const promptGradient = ctx.createLinearGradient(-promptWidth / 2, -promptHeight / 2, promptWidth / 2, promptHeight / 2)
    promptGradient.addColorStop(0, '#FF6B9D')
    promptGradient.addColorStop(1, '#FF8FB3')

    ctx.fillStyle = promptGradient
    this.drawRoundedRect(ctx, -promptWidth / 2, -promptHeight / 2, promptWidth, promptHeight, 25)
    ctx.fill()

    // 边框
    ctx.strokeStyle = '#FF6B9D'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, -promptWidth / 2, -promptHeight / 2, promptWidth, promptHeight, 25)
    ctx.stroke()

    // 提示文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('🎮 点击屏幕开始游戏', 0, 0)

    ctx.restore()
  }

  /**
   * 渲染用户信息卡片 - 美化版本
   */
  renderUserInfoCard(ctx, centerX, centerY) {
    const saveData = this.saveManager.getSaveData()
    const userInfo = this.userManager.getUserInfo()
    const cardWidth = 320
    const cardHeight = 100

    // 卡片阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.15)'
    this.drawRoundedRect(ctx, centerX - cardWidth / 2 + 4, centerY - cardHeight / 2 + 4, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片渐变背景
    const cardGradient = ctx.createLinearGradient(
      centerX - cardWidth / 2, centerY - cardHeight / 2,
      centerX + cardWidth / 2, centerY + cardHeight / 2
    )
    cardGradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)')
    cardGradient.addColorStop(0.5, 'rgba(240, 248, 255, 0.95)')
    cardGradient.addColorStop(1, 'rgba(230, 240, 255, 0.95)')

    ctx.fillStyle = cardGradient
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.fill()

    // 卡片边框
    ctx.strokeStyle = 'rgba(74, 144, 226, 0.4)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, centerY - cardHeight / 2, cardWidth, cardHeight, 15)
    ctx.stroke()

    // 显示用户信息
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 用户昵称
    if (userInfo && userInfo.profile && userInfo.profile.nickName) {
      ctx.fillStyle = '#4CAF50'
      ctx.font = 'bold 16px Arial'
      ctx.fillText(`👤 ${userInfo.profile.nickName}`, centerX, centerY - 25)

      // 服务器连接状态
      if (this.userManager.isConnectedToServer()) {
        if (this.isLoadingServerProgress) {
          ctx.fillStyle = '#FF9800'
          ctx.font = '12px Arial'
          ctx.fillText('🔄 正在同步进度...', centerX, centerY - 5)
        } else if (this.serverProgress) {
          ctx.fillStyle = '#2196F3'
          ctx.font = '12px Arial'
          ctx.fillText('🌐 云端已同步', centerX, centerY - 5)
        } else {
          ctx.fillStyle = '#F44336'
          ctx.font = '12px Arial'
          ctx.fillText('⚠️ 同步失败', centerX, centerY - 5)
        }
      } else {
        ctx.fillStyle = '#FF9800'
        ctx.font = '12px Arial'
        ctx.fillText('📱 本地模式', centerX, centerY - 5)
      }
    } else {
      ctx.fillStyle = '#666666'
      ctx.font = '16px Arial'
      ctx.fillText('👤 游客模式', centerX, centerY - 20)
    }

    // 游戏进度信息 - 统一使用与关卡选择页相同的数据源
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 14px Arial'

    // 优先使用服务器进度，如果没有则使用本地进度
    let maxLevel, totalScore
    if (this.serverProgress) {
      maxLevel = this.serverProgress.maxLevel
      totalScore = this.serverProgress.totalScore || 0
    } else {
      maxLevel = saveData.maxLevel
      totalScore = saveData.totalScore
    }

    ctx.fillText(`🏆 最高关卡: ${maxLevel}`, centerX - 80, centerY + 20)
    ctx.fillText(`💎 总得分: ${totalScore}`, centerX + 80, centerY + 20)
  }

  /**
   * 渲染关卡选择界面
   */
  renderLevelSelectScreen() {
    const ctx = this.ctx
    const centerX = this.canvasSize.screenWidth / 2
    const time = Date.now() * 0.001 // 用于动画

    // 优先使用服务器进度，如果没有则使用本地进度
    let maxLevel, totalScore
    if (this.serverProgress) {
      maxLevel = this.serverProgress.maxLevel
      totalScore = this.serverProgress.totalScore || 0
    } else {
      maxLevel = this.saveManager.getMaxLevel()
      const saveData = this.saveManager.getSaveData()
      totalScore = saveData.totalScore
    }

    const totalLevels = 20 // 游戏总共有20关，无论是否解锁都要显示

    // 渲染增强版标题区域
    this.renderLevelSelectHeader(ctx, centerX, time, maxLevel, totalScore)

    // 渲染关卡网格
    this.renderLevelGrid(ctx, centerX, time, maxLevel, totalLevels)
  }

  /**
   * 渲染关卡选择标题区域
   */
  renderLevelSelectHeader(ctx, centerX, time, maxLevel, totalScore) {
    // 增强版标题背景 - 更丰富的渐变，增加高度避免UI重叠
    const titleGradient = ctx.createLinearGradient(0, 0, this.canvasSize.screenWidth, 140)
    titleGradient.addColorStop(0, 'rgba(74, 144, 226, 0.9)')
    titleGradient.addColorStop(0.3, 'rgba(80, 227, 194, 0.9)')
    titleGradient.addColorStop(0.7, 'rgba(184, 233, 134, 0.9)')
    titleGradient.addColorStop(1, 'rgba(255, 182, 193, 0.9)')

    ctx.fillStyle = titleGradient
    ctx.fillRect(0, 0, this.canvasSize.screenWidth, 140)

    // 添加动态装饰元素
    this.renderHeaderDecorations(ctx, centerX, time)

    // 美化标题文字 - 向下移动避免刘海屏遮挡
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 8
    ctx.shadowOffsetX = 2
    ctx.shadowOffsetY = 2

    ctx.fillStyle = '#FFFFFF'
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)'
    ctx.lineWidth = 3
    ctx.font = 'bold 38px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.strokeText('🎮 关卡选择', centerX, 55)
    ctx.fillText('🎮 关卡选择', centerX, 55)
    ctx.restore()

    // 美化进度信息显示
    this.renderProgressInfo(ctx, centerX, maxLevel, totalScore)
  }

  /**
   * 渲染标题装饰元素
   */
  renderHeaderDecorations(ctx, centerX, time) {
    // 浮动的光效
    ctx.globalAlpha = 0.4
    for (let i = 0; i < 5; i++) {
      const x = centerX + Math.sin(time * 1.5 + i * 1.2) * 120
      const y = 60 + Math.cos(time * 0.8 + i * 0.8) * 20
      const radius = 12 + Math.sin(time * 2 + i) * 4

      const sparkleGradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      sparkleGradient.addColorStop(0, '#FFFFFF')
      sparkleGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.5)')
      sparkleGradient.addColorStop(1, 'transparent')

      ctx.fillStyle = sparkleGradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }

    // 添加星星装饰
    for (let i = 0; i < 3; i++) {
      const x = 50 + i * (this.canvasSize.screenWidth - 100) / 2
      const y = 25 + Math.sin(time * 2 + i * 2) * 8
      const size = 6 + Math.sin(time * 3 + i) * 2

      ctx.fillStyle = '#FFD700'
      this.drawStar(ctx, x, y, size, 5)
    }

    ctx.globalAlpha = 1
  }



  /**
   * 渲染进度信息
   */
  renderProgressInfo(ctx, centerX, maxLevel, totalScore) {
    // 进度信息背景
    const infoWidth = 300
    const infoHeight = 30
    const infoY = 95  // 去掉返回按钮后，可以向上移动

    // 半透明背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, centerX - infoWidth / 2, infoY - infoHeight / 2, infoWidth, infoHeight, 15)
    ctx.fill()

    // 进度文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    let progressText
    if (this.serverProgress) {
      progressText = `🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore} 🌐`
    } else {
      progressText = `🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore}`
    }

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(progressText, centerX, infoY)
    ctx.restore()
  }

  /**
   * 渲染关卡网格
   */
  renderLevelGrid(ctx, centerX, time, maxLevel, totalLevels) {
    // 关卡按钮布局 (4列布局，更美观)
    const cols = 4
    const buttonSize = 60 // 调整按钮大小以适应更多关卡
    const spacing = 20 // 调整间距
    const startY = 170 // 增加间距，避免与标题栏重叠（标题栏高度140px + 30px间距）
    const totalWidth = cols * buttonSize + (cols - 1) * spacing
    const startX = centerX - totalWidth / 2

    // 计算可显示的行数 - 确保能显示20关
    const availableHeight = this.canvasSize.screenHeight - startY - 80
    const maxRows = Math.floor(availableHeight / (buttonSize + spacing + 35)) // 35是星星和文字的空间
    const levelsPerPage = cols * maxRows

    // 简单分页
    const currentPage = 0
    const startLevel = currentPage * levelsPerPage + 1
    const endLevel = Math.min(startLevel + levelsPerPage - 1, totalLevels)

    // 绘制美化的网格背景
    this.renderGridBackground(ctx, startX, startY, totalWidth, maxRows, buttonSize, spacing)

    // 渲染关卡按钮
    this.renderLevelButtons(ctx, startX, startY, buttonSize, spacing, cols, startLevel, endLevel, maxLevel, time)

    // 显示分页信息（如果有多页）
    if (totalLevels > levelsPerPage) {
      this.renderPaginationInfo(ctx, centerX, currentPage, totalLevels, levelsPerPage)
    }
  }

  /**
   * 渲染网格背景
   */
  renderGridBackground(ctx, startX, startY, totalWidth, maxRows, buttonSize, spacing) {
    const gridHeight = maxRows * (buttonSize + spacing + 40) + 20
    const padding = 20

    // 背景阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, startX - padding + 3, startY - padding + 3, totalWidth + padding * 2, gridHeight, 15)
    ctx.fill()

    // 背景渐变
    const bgGradient = ctx.createLinearGradient(startX - padding, startY - padding, startX - padding, startY + gridHeight)
    bgGradient.addColorStop(0, 'rgba(255, 255, 255, 0.15)')
    bgGradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)')

    ctx.fillStyle = bgGradient
    this.drawRoundedRect(ctx, startX - padding, startY - padding, totalWidth + padding * 2, gridHeight, 15)
    ctx.fill()

    // 背景边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1
    this.drawRoundedRect(ctx, startX - padding, startY - padding, totalWidth + padding * 2, gridHeight, 15)
    ctx.stroke()
  }

  /**
   * 渲染关卡按钮
   */
  renderLevelButtons(ctx, startX, startY, buttonSize, spacing, cols, startLevel, endLevel, maxLevel, time) {
    for (let i = startLevel; i <= endLevel; i++) {
      const index = i - startLevel
      const row = Math.floor(index / cols)
      const col = index % cols
      const buttonX = startX + col * (buttonSize + spacing)
      const buttonY = startY + row * (buttonSize + spacing + 40)

      // 检查关卡是否已解锁 - 优先使用服务器数据
      let isUnlocked, record
      if (this.serverProgress && this.serverProgress.records) {
        isUnlocked = i <= maxLevel
        record = this.serverProgress.records.find(r => r.level === i)
      } else {
        isUnlocked = this.saveManager.isLevelUnlocked(i)
        record = this.saveManager.getLevelRecord(i)
      }

      // 渲染单个关卡按钮
      this.renderSingleLevelButton(ctx, buttonX, buttonY, buttonSize, i, isUnlocked, record, time)
    }
  }

  /**
   * 渲染单个关卡按钮
   */
  renderSingleLevelButton(ctx, buttonX, buttonY, buttonSize, level, isUnlocked, record, time) {
    // 添加按钮悬浮动画效果
    const hoverOffset = Math.sin(time * 2 + level * 0.5) * 2
    const finalY = buttonY + hoverOffset

    // 绘制增强版按钮阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.25)'
    this.drawRoundedRect(ctx, buttonX + 4, finalY + 4, buttonSize, buttonSize, 12)
    ctx.fill()

    if (isUnlocked) {
      // 已解锁关卡
      const primaryColor = record ? '#4CAF50' : '#2196F3'
      const secondaryColor = record ? '#66BB6A' : '#42A5F5'

      // 绘制关卡按钮
      this.drawEnhancedLevelButton(ctx, buttonX, finalY, buttonSize, level.toString(), primaryColor, secondaryColor, true)

      // 显示星级评价
      if (record) {
        const stars = this.calculateStars(record)
        this.drawEnhancedStars(ctx, buttonX + buttonSize / 2, finalY + buttonSize + 18, stars)

        // 显示最佳成绩
        ctx.fillStyle = '#555555'
        ctx.font = 'bold 12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(`${record.moves}步`, buttonX + buttonSize / 2, finalY + buttonSize + 35)
      }
    } else {
      // 未解锁关卡
      this.drawEnhancedLevelButton(ctx, buttonX, finalY, buttonSize, '🔒', '#CCCCCC', '#E0E0E0', false)

      // 显示解锁条件
      ctx.fillStyle = '#999999'
      ctx.font = '11px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('未解锁', buttonX + buttonSize / 2, finalY + buttonSize + 25)
    }
  }

  /**
   * 渲染分页信息
   */
  renderPaginationInfo(ctx, centerX, currentPage, totalLevels, levelsPerPage) {
    const totalPages = Math.ceil(totalLevels / levelsPerPage)

    // 分页信息背景
    const infoWidth = 200
    const infoHeight = 30
    const infoY = this.canvasSize.screenHeight - 40

    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    this.drawRoundedRect(ctx, centerX - infoWidth / 2, infoY - infoHeight / 2, infoWidth, infoHeight, 15)
    ctx.fill()

    // 分页文字
    ctx.fillStyle = '#666666'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(`第 ${currentPage + 1} 页 / 共 ${totalPages} 页`, centerX, infoY)
  }

  /**
   * 绘制增强版关卡按钮
   */
  drawEnhancedLevelButton(ctx, x, y, size, text, primaryColor, secondaryColor, isEnabled) {
    // 绘制按钮渐变背景
    const gradient = ctx.createLinearGradient(x, y, x, y + size)
    gradient.addColorStop(0, primaryColor)
    gradient.addColorStop(0.5, secondaryColor)
    gradient.addColorStop(1, primaryColor)

    ctx.fillStyle = gradient
    this.drawRoundedRect(ctx, x, y, size, size, 12)
    ctx.fill()

    // 绘制按钮边框
    if (isEnabled) {
      ctx.strokeStyle = primaryColor
      ctx.lineWidth = 3
    } else {
      ctx.strokeStyle = '#BBBBBB'
      ctx.lineWidth = 2
    }
    this.drawRoundedRect(ctx, x, y, size, size, 12)
    ctx.stroke()

    // 添加高光效果
    if (isEnabled) {
      const highlightGradient = ctx.createLinearGradient(x, y, x, y + size / 2)
      highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)')
      highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

      ctx.fillStyle = highlightGradient
      this.drawRoundedRect(ctx, x, y, size, size / 2, 12)
      ctx.fill()
    }

    // 绘制按钮文字
    ctx.fillStyle = isEnabled ? '#FFFFFF' : '#888888'
    ctx.font = 'bold 20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    if (isEnabled) {
      ctx.save()
      ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
      ctx.shadowBlur = 2
      ctx.shadowOffsetX = 1
      ctx.shadowOffsetY = 1
    }

    ctx.fillText(text, x + size / 2, y + size / 2)

    if (isEnabled) {
      ctx.restore()
    }
  }

  /**
   * 绘制增强版星星评价
   */
  drawEnhancedStars(ctx, centerX, y, starCount) {
    const starSize = 10
    const spacing = 18
    const totalWidth = 3 * starSize + 2 * spacing
    const startX = centerX - totalWidth / 2

    ctx.save()
    for (let i = 0; i < 3; i++) {
      const x = startX + i * spacing
      if (i < starCount) {
        // 金色星星
        ctx.fillStyle = '#FFD700'
        ctx.strokeStyle = '#FFA500'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      } else {
        // 灰色星星
        ctx.fillStyle = '#DDDDDD'
        ctx.strokeStyle = '#CCCCCC'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      }
    }
    ctx.restore()
  }

  /**
   * 渲染游戏界面
   */
  renderGameScreen() {
    // 每帧渲染前更新可点击状态，保证状态与视觉同步
    this.board.updateClickableStates()
    // 渲染UI信息
    this.renderGameUI()
    
    // 渲染游戏板
    if (this.board) {
      this.board.render(this.ctx, this.resources)
      
      // 如果启用调试模式，渲染层级信息
      if (this.debugMode) {
        this.renderDebugInfo()
      }
    }
    
    // 渲染槽位
    if (this.slot) {
      this.slot.render(this.ctx, this.resources)
    }
    
    // 渲染水印
    this.renderWatermark()
    
    // 渲染道具按钮
    this.powerUpButtons.forEach(button => {
      button.render(this.ctx)
    })
    
    // 渲染设置按钮
    if (this.settingsButton) {
      this.settingsButton.render(this.ctx)
    }
    
    // 渲染设置面板（最上层）
    if (this.settingsPanel) {
      this.settingsPanel.render(this.ctx)
    }
    
    // 渲染分享对话框（最最上层）
    if (this.shareDialog && this.shareDialog.visible) {
      this.shareDialog.render(this.ctx)
    }
  }

  /**
   * 渲染游戏UI
   */
  renderGameUI() {
    const ctx = this.ctx
    const time = Date.now() * 0.001

    // 渲染增强版顶部信息栏
    this.renderEnhancedTopBar(ctx, time)

    // 渲染游戏状态信息
    this.renderGameStatusInfo(ctx, time)
  }

  /**
   * 渲染增强版顶部信息栏
   */
  renderEnhancedTopBar(ctx, time) {
    const barHeight = 100  // 从70增加到100，扩大标签栏高度

    // 顶部信息栏渐变背景
    const topGradient = ctx.createLinearGradient(0, 0, 0, barHeight)
    topGradient.addColorStop(0, 'rgba(74, 144, 226, 0.9)')
    topGradient.addColorStop(0.5, 'rgba(80, 227, 194, 0.8)')
    topGradient.addColorStop(1, 'rgba(184, 233, 134, 0.7)')

    ctx.fillStyle = topGradient
    ctx.fillRect(0, 0, this.canvasSize.screenWidth, barHeight)

    // 添加装饰光效
    ctx.globalAlpha = 0.3
    for (let i = 0; i < 3; i++) {
      const x = (i + 1) * this.canvasSize.screenWidth / 4 + Math.sin(time + i * 2) * 20
      const y = barHeight / 2
      const sparkleGradient = ctx.createRadialGradient(x, y, 0, x, y, 15)
      sparkleGradient.addColorStop(0, '#FFFFFF')
      sparkleGradient.addColorStop(1, 'transparent')

      ctx.fillStyle = sparkleGradient
      ctx.beginPath()
      ctx.arc(x, y, 10 + Math.sin(time * 3 + i) * 3, 0, Math.PI * 2)
      ctx.fill()
    }
    ctx.globalAlpha = 1

    // 底部边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(0, barHeight)
    ctx.lineTo(this.canvasSize.screenWidth, barHeight)
    ctx.stroke()
  }

  /**
   * 渲染游戏状态信息
   */
  renderGameStatusInfo(ctx, time) {
    const centerX = this.canvasSize.screenWidth / 2
    const barHeight = 100  // 更新为新的标签栏高度

    // 关卡信息（右上角）
    this.renderLevelInfo(ctx, time)

    // 分数信息（居中，稍微往下调整）
    this.renderScoreInfo(ctx, centerX, barHeight / 2 + 5, time)  // 调整分数位置

    // 移动次数（左上角）
    this.renderMovesInfo(ctx, time)
  }

  /**
   * 渲染关卡信息
   */
  renderLevelInfo(ctx, time) {
    const x = this.canvasSize.screenWidth - 25
    const y = 50  // 从35增加到50，往下移动15px

    // 关卡信息背景
    const bgWidth = 100
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, x - bgWidth, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 关卡文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`第${this.currentLevel}关`, x - 10, y)
    ctx.restore()
  }

  /**
   * 渲染分数信息
   */
  renderScoreInfo(ctx, centerX, centerY, time) {
    // 分数背景
    const bgWidth = 120
    const bgHeight = 35

    // 脉冲效果
    const pulseScale = 1 + Math.sin(time * 4) * 0.05

    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.scale(pulseScale, pulseScale)

    // 分数背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.25)'
    this.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.fill()

    // 分数边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.stroke()

    // 分数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.7)'
    ctx.shadowBlur = 4
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`💎 ${this.score}`, 0, 0)

    ctx.restore()
  }

  /**
   * 渲染移动次数信息
   */
  renderMovesInfo(ctx, time) {
    const x = 25
    const y = 35

    // 移动次数背景
    const bgWidth = 80
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    this.drawRoundedRect(ctx, x, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 移动次数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`🎯 ${this.moves}`, x + 10, y)
    ctx.restore()
  }

  /**
   * 渲染水印
   */
  renderWatermark() {
    const ctx = this.ctx
    const centerX = this.canvasSize.screenWidth / 2
    
    // 计算水印位置（游戏板下方，道具按钮上方的空白区域）
    const gameboardBottom = 80 + this.canvasSize.screenHeight * 0.5 // 游戏板底部
    const slotTop = this.canvasSize.screenHeight - 70 - 70 // 槽位顶部
    const watermarkY = gameboardBottom + (slotTop - gameboardBottom) / 2 // 中间位置
    
    // 保存当前状态
    ctx.save()
    
    // 设置透明度，让水印不太突兀
    ctx.globalAlpha = 0.6
    
    // 创建渐变文字效果
    const gradient = ctx.createLinearGradient(centerX - 100, watermarkY - 20, centerX + 100, watermarkY + 20)
    gradient.addColorStop(0, '#FF6B9D')    // 粉色
    gradient.addColorStop(0.3, '#FFD700')  // 金色
    gradient.addColorStop(0.6, '#FF8FB3')  // 浅粉色
    gradient.addColorStop(1, '#FFA500')    // 橙色
    
    // 绘制水印文字
    ctx.fillStyle = gradient
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.lineWidth = 2
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    // 先绘制描边
    ctx.strokeText('鸭了个鸭呀', centerX, watermarkY)
    // 再绘制填充
    ctx.fillText('鸭了个鸭呀', centerX, watermarkY)
    
    // 添加一些装饰效果
    const time = Date.now() * 0.003
    
    // 绘制小星星装饰
    ctx.globalAlpha = 0.4
    for (let i = 0; i < 3; i++) {
      const offsetX = Math.sin(time + i * 2) * 80
      const offsetY = Math.cos(time * 0.5 + i) * 10
      const starX = centerX + offsetX
      const starY = watermarkY + offsetY
      const starSize = 4 + Math.sin(time * 2 + i) * 2
      
      ctx.fillStyle = '#FFD700'
      this.drawStar(ctx, starX, starY, starSize, 5)
    }
    
    // 恢复状态
    ctx.restore()
  }

  /**
   * 渲染胜利界面
   */
  renderWinScreen() {
    // 先渲染游戏界面作为背景
    this.renderGameScreen()

    // 使用新的胜利页面组件渲染
    if (this.winScreen) {
      this.winScreen.render(this.ctx)
    }
  }

  /**
   * 渲染失败界面
   */
  renderLoseScreen() {
    // 先渲染游戏界面
    this.renderGameScreen()
    
    // 添加失败遮罩
    const ctx = this.ctx
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    ctx.fillRect(0, 0, this.canvasSize.screenWidth, this.canvasSize.screenHeight)
    
    // 失败文字
    ctx.fillStyle = '#FF6B6B'
    ctx.strokeStyle = '#8B0000'
    ctx.lineWidth = 3
    ctx.font = 'bold 36px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    ctx.strokeText('游戏失败', centerX, centerY - 50)
    ctx.fillText('游戏失败', centerX, centerY - 50)
    
    // 游戏统计 - 修复时间计算，确保不为负数
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '18px Arial'
    
    // 计算用时，确保不为负数
    let playTime = 0
    if (this.endTime > 0 && this.startTime > 0 && this.endTime >= this.startTime) {
      playTime = Math.floor((this.endTime - this.startTime) / 1000)
    }
    
    ctx.fillText(`用时: ${playTime}秒`, centerX, centerY + 30)
    ctx.fillText(`总分: ${this.score}`, centerX, centerY + 60)
    
    // 重试提示
    ctx.font = '16px Arial'
    ctx.fillText('点击重新开始', centerX, centerY + 110)
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    if (!this.board) return
    
    const ctx = this.ctx
    ctx.save()
    
    // 渲染方块层级数字
    this.board.blocks.forEach(block => {
      if (block.isVisible) {
        // 绘制层级数字背景
        ctx.fillStyle = block.isClickable ? 'rgba(0, 255, 0, 0.8)' : 'rgba(255, 0, 0, 0.8)'
        ctx.fillRect(block.x - 2, block.y - 2, 16, 16)
        
        // 绘制层级数字
        ctx.fillStyle = '#FFFFFF'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(block.layer.toString(), block.x + 6, block.y + 10)
        
        // 如果不可点击，在方块上绘制红色X
        if (!block.isClickable) {
          ctx.strokeStyle = '#FF0000'
          ctx.lineWidth = 3
          ctx.beginPath()
          ctx.moveTo(block.x + 5, block.y + 5)
          ctx.lineTo(block.x + block.width - 5, block.y + block.height - 5)
          ctx.moveTo(block.x + block.width - 5, block.y + 5)
          ctx.lineTo(block.x + 5, block.y + block.height - 5)
          ctx.stroke()
        }
      }
    })
    
    // 显示调试信息面板
    const debugPanelY = this.canvasSize.screenHeight - 160
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.fillRect(0, debugPanelY, this.canvasSize.screenWidth, 160)
    
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '14px Arial'
    ctx.textAlign = 'left'
    
    const totalBlocks = this.board.blocks.length
    const visibleBlocks = this.board.blocks.filter(b => b.isVisible).length
    const clickableBlocks = this.board.blocks.filter(b => b.isVisible && b.isClickable).length
    
    ctx.fillText(`调试信息:`, 10, debugPanelY + 20)
    ctx.fillText(`总方块数: ${totalBlocks}`, 10, debugPanelY + 40)
    ctx.fillText(`可见方块: ${visibleBlocks}`, 10, debugPanelY + 60)
    ctx.fillText(`可点击方块: ${clickableBlocks}`, 10, debugPanelY + 80)
    
    // 显示层级分布
    const layerStats = new Map()
    this.board.blocks.forEach(block => {
      if (block.isVisible) {
        const count = layerStats.get(block.layer) || 0
        layerStats.set(block.layer, count + 1)
      }
    })
    
    let y = debugPanelY + 100
    ctx.fillText(`层级分布:`, 10, y)
    y += 20
    layerStats.forEach((count, layer) => {
      ctx.fillText(`层级${layer}: ${count}个方块`, 10, y)
      y += 15
    })
    
    ctx.restore()
  }


  /**
   * 计算关卡星级评价
   */
  calculateStars(record) {
    // 简单的星级评价算法
    if (record.moves <= 20) return 3
    if (record.moves <= 35) return 2
    return 1
  }

  /**
   * 加载服务器进度数据
   */
  async loadServerProgress() {
    // 避免重复加载
    if (this.isLoadingServerProgress) {
      return
    }
    
    let requestParams = null
    let serverResponse = null
    let networkDetails = null
    
    try {
      if (this.userManager && this.userManager.isConnectedToServer()) {
        logger.info('📊 加载服务器进度数据...')
        this.isLoadingServerProgress = true
        
        // 获取网络管理器的详细信息
        const networkManager = this.userManager.networkManager
        const baseURL = networkManager.baseURL
        const timeout = networkManager.timeout
        const serverUserId = this.userManager.getServerUserId()
        
        // 解析服务器地址信息
        let serverHost = 'unknown'
        let serverPort = 'unknown'
        let protocol = 'unknown'
        try {
          const urlObj = new URL(baseURL)
          protocol = urlObj.protocol.replace(':', '')
          serverHost = urlObj.hostname
          serverPort = urlObj.port || (protocol === 'https' ? '443' : '80')
        } catch (e) {
          logger.warn('无法解析服务器URL:', baseURL)
        }
        
        // 构建完整的请求URL
        const requestPath = '/api/user/progress'
        const queryParams = `user_id=${serverUserId}`
        const fullRequestUrl = `${baseURL}${requestPath}?${queryParams}`
        
        // 记录详细的网络请求信息
        networkDetails = {
          server_info: {
            base_url: baseURL,
            protocol: protocol,
            host: serverHost,
            port: serverPort
          },
          request_info: {
            method: 'GET',
            path: requestPath,
            query_params: queryParams,
            full_url: fullRequestUrl,
            timeout: timeout,
            headers: {
              'content-type': 'application/json'
            }
          }
        }
        
        // 记录请求参数
        requestParams = {
          userId: serverUserId,
          method: 'getUserProgress',
          timestamp: new Date().toISOString(),
          network_details: networkDetails
        }
        
        logger.info('📤 详细请求信息:')
        logger.info('   🌐 服务器地址:', baseURL)
        logger.info('   🏠 服务器主机:', serverHost)
        logger.info('   🚪 服务器端口:', serverPort)
        logger.info('   🔗 协议类型:', protocol)
        logger.info('   📍 请求路径:', requestPath)
        logger.info('   🔍 查询参数:', queryParams)
        logger.info('   🌍 完整URL:', fullRequestUrl)
        logger.info('   ⏱️ 超时时间:', timeout + 'ms')
        logger.info('   👤 用户ID:', serverUserId)
        logger.info('   📤 完整请求参数:', requestParams)
        
        const result = await this.userManager.getUserProgress()
        
        // 记录服务器响应
        serverResponse = result
        logger.info('📥 详细服务器响应:')
        logger.info('   ✅ 响应状态:', result.success ? '成功' : '失败')
        logger.info('   📊 响应数据:', serverResponse)
        
        if (result.success) {
          this.serverProgress = result.data
          logger.info('✅ 服务器进度加载成功:', this.serverProgress)
          
          // 可以在这里同步本地存档与服务器数据
          this.syncProgressWithServer()
        } else {
          logger.warn('⚠️ 加载服务器进度失败:', result.error)
          this.serverProgress = null
        }
      } else {
        // 记录未连接原因的详细信息
        const userManagerExists = !!this.userManager
        const isConnected = userManagerExists ? this.userManager.isConnectedToServer() : false
        const userInfo = userManagerExists ? this.userManager.getUserInfo() : null
        const serverUserId = userManagerExists ? this.userManager.getServerUserId() : null
        
        // 尝试获取网络配置信息（即使未连接）
        let networkConfig = null
        if (userManagerExists && this.userManager.networkManager) {
          const networkManager = this.userManager.networkManager
          const baseURL = networkManager.baseURL
          
          // 解析服务器地址信息
          let serverHost = 'unknown'
          let serverPort = 'unknown'
          let protocol = 'unknown'
          try {
            const urlObj = new URL(baseURL)
            protocol = urlObj.protocol.replace(':', '')
            serverHost = urlObj.hostname
            serverPort = urlObj.port || (protocol === 'https' ? '443' : '80')
          } catch (e) {
            logger.warn('无法解析服务器URL:', baseURL)
          }
          
          networkConfig = {
            base_url: baseURL,
            protocol: protocol,
            host: serverHost,
            port: serverPort,
            timeout: networkManager.timeout
          }
        }
        
        requestParams = {
          intended_method: 'getUserProgress',
          intended_user_id: serverUserId,
          timestamp: new Date().toISOString(),
          network_config: networkConfig,
          connection_status: {
            userManager_exists: userManagerExists,
            is_connected_to_server: isConnected,
            has_user_info: !!userInfo,
            server_user_id: serverUserId
          }
        }
        
        serverResponse = {
          success: false,
          error: '未连接到服务器',
          reason: !userManagerExists ? 'userManager不存在' : !isConnected ? '服务器连接失败' : '未知原因'
        }
        
        logger.info('🔒 未连接到服务器，使用本地进度')
        logger.info('📤 尝试请求的详细信息:')
        if (networkConfig) {
          logger.info('   🌐 目标服务器:', networkConfig.base_url)
          logger.info('   🏠 目标主机:', networkConfig.host)
          logger.info('   🚪 目标端口:', networkConfig.port)
          logger.info('   🔗 协议类型:', networkConfig.protocol)
          logger.info('   ⏱️ 超时配置:', networkConfig.timeout + 'ms')
        } else {
          logger.info('   ❌ 无法获取网络配置信息')
        }
        logger.info('   👤 目标用户ID:', serverUserId)
        logger.info('   📍 目标路径: /api/user/progress')
        logger.info('   🔍 查询参数: user_id=' + serverUserId)
        logger.info('   📤 完整请求参数:', requestParams)
        logger.info('📥 失败响应信息:', serverResponse)
        
        this.serverProgress = null
      }
    } catch (error) {
      // 记录异常时的请求和响应信息
      serverResponse = {
        success: false,
        error: error.message,
        exception: true,
        stack: error.stack
      }
      
      logger.error('❌ 加载服务器进度异常:', error)
      logger.info('📤 异常时的请求参数:', requestParams)
      logger.info('🌐 异常时的网络详情:', networkDetails)
      logger.info('📥 异常时的响应信息:', serverResponse)
      
      this.serverProgress = null
    } finally {
      this.isLoadingServerProgress = false
    }
  }

  /**
   * 同步本地进度与服务器进度
   */
  syncProgressWithServer() {
    if (!this.serverProgress) {
      return
    }
    
    try {
      const localSave = this.saveManager.getSaveData()
      
      // 如果服务器的最高关卡比本地高，同步到本地
      if (this.serverProgress.maxLevel > localSave.maxLevel) {
        logger.info(`🔄 同步服务器最高关卡: ${this.serverProgress.maxLevel}`)
        this.saveManager.saveGame({
          currentLevel: this.serverProgress.maxLevel + 1,
          maxLevel: this.serverProgress.maxLevel,
          totalScore: Math.max(localSave.totalScore, this.serverProgress.totalScore || 0)
        })
      }
    } catch (error) {
      logger.error('❌ 同步进度失败:', error)
    }
  }

  /**
   * 绘制用户信息卡片
   */
  drawUserInfoCard(ctx, centerX) {
    const saveData = this.saveManager.getSaveData()
    const userInfo = this.userManager.getUserInfo()
    const cardY = 120
    const cardWidth = 300
    const cardHeight = 80 // 增加高度以容纳更多信息
    
    // 绘制卡片背景
    const cardGradient = ctx.createLinearGradient(
      centerX - cardWidth / 2, cardY,
      centerX + cardWidth / 2, cardY + cardHeight
    )
    cardGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)')
    cardGradient.addColorStop(1, 'rgba(240, 248, 255, 0.9)')
    
    ctx.fillStyle = cardGradient
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, cardY, cardWidth, cardHeight, 10)
    ctx.fill()
    
    // 绘制卡片边框
    ctx.strokeStyle = 'rgba(74, 144, 226, 0.3)'
    ctx.lineWidth = 2
    this.drawRoundedRect(ctx, centerX - cardWidth / 2, cardY, cardWidth, cardHeight, 10)
    ctx.stroke()
    
    // 显示用户信息
    ctx.textAlign = 'center'
    
    // 用户昵称和登录状态
    if (userInfo && userInfo.profile && userInfo.profile.nickName) {
      ctx.fillStyle = '#4CAF50'
      ctx.font = '14px Arial'
      ctx.fillText(`👤 ${userInfo.profile.nickName}`, centerX, cardY + 18)
      
      // 显示服务器连接状态和进度同步状态
      if (this.userManager.isConnectedToServer()) {
        if (this.isLoadingServerProgress) { // 优先显示正在加载
          ctx.fillStyle = '#FF9800'
          ctx.font = '12px Arial'
          ctx.fillText('🔄 正在同步进度...', centerX, cardY + 35)
        } else if (this.serverProgress) { // 然后检查是否有服务器进度
          ctx.fillStyle = '#2196F3'
          ctx.font = '12px Arial'
          ctx.fillText('🌐 服务器已同步', centerX, cardY + 35)
        } else { // 如果已连接但没有进度且不在加载中，则认为同步失败
          ctx.fillStyle = '#F44336' // 更改为更明显的红色
          ctx.font = '12px Arial'
          ctx.fillText('⚠️ 进度同步失败', centerX, cardY + 35)
        }
      } else {
        ctx.fillStyle = '#FF9800'
        ctx.font = '12px Arial'
        ctx.fillText('🔌 未连接到服务器', centerX, cardY + 35) // 更明确的未连接提示
      }
    } else {
      // 显示未授权状态和登录按钮
      ctx.fillStyle = '#666666'
      ctx.font = '14px Arial'
      ctx.fillText('👤 游客用户', centerX, cardY + 20)
      
      // 绘制授权登录按钮
      const buttonY = cardY + 40
      const buttonWidth = 120
      const buttonHeight = 24
      
      // 绘制按钮背景
      const buttonGradient = ctx.createLinearGradient(
        centerX - buttonWidth / 2, buttonY - buttonHeight / 2,
        centerX + buttonWidth / 2, buttonY + buttonHeight / 2
      )
      buttonGradient.addColorStop(0, '#4CAF50')
      buttonGradient.addColorStop(1, '#45a049')
      
      ctx.fillStyle = buttonGradient
      this.drawRoundedRect(ctx, centerX - buttonWidth / 2, buttonY - buttonHeight / 2, buttonWidth, buttonHeight, 12)
      ctx.fill()
      
      // 绘制按钮边框
      ctx.strokeStyle = '#4CAF50'
      ctx.lineWidth = 1
      this.drawRoundedRect(ctx, centerX - buttonWidth / 2, buttonY - buttonHeight / 2, buttonWidth, buttonHeight, 12)
      ctx.stroke()
      
      // 绘制按钮文字
      ctx.fillStyle = '#FFFFFF'
      ctx.font = 'bold 12px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText('获取用户信息', centerX, buttonY)
      
      // 保存按钮区域用于点击检测
      this.loginButtonArea = {
        x: centerX - buttonWidth / 2,
        y: buttonY - buttonHeight / 2,
        width: buttonWidth,
        height: buttonHeight
      }
    }
    
    // 显示进度信息 - 优先使用服务器数据
    ctx.fillStyle = '#666666'
    ctx.font = '13px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'alphabetic'
    const progressY = userInfo?.profile?.nickName ? cardY + 60 : cardY + 70
    
    let maxLevel, totalScore
    if (this.serverProgress) {
      maxLevel = this.serverProgress.maxLevel
      totalScore = this.serverProgress.totalScore || 0
      // 添加服务器数据标识
      ctx.fillText(`🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore} 🌐`, centerX, progressY)
    } else {
      maxLevel = saveData.maxLevel
      totalScore = saveData.totalScore
      ctx.fillText(`🏆 最高关卡: ${maxLevel}   💎 总得分: ${totalScore}`, centerX, progressY)
    }
  }

  /**
   * 绘制现代化按钮
   */
  drawModernButton(ctx, x, y, width, height, text, primaryColor, secondaryColor) {
    // 绘制按钮渐变背景
    const gradient = ctx.createLinearGradient(x, y, x, y + height)
    gradient.addColorStop(0, primaryColor)
    gradient.addColorStop(1, secondaryColor)
    
    ctx.fillStyle = gradient
    this.drawRoundedRect(ctx, x - width / 2, y - height / 2, width, height, height / 2)
    ctx.fill()
    
    // 绘制按钮边框
    ctx.strokeStyle = primaryColor
    ctx.lineWidth = 1
    this.drawRoundedRect(ctx, x - width / 2, y - height / 2, width, height, height / 2)
    ctx.stroke()
    
    // 绘制按钮文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, x, y)
  }

  /**
   * 绘制关卡按钮
   */
  drawLevelButton(ctx, x, y, size, text, primaryColor, secondaryColor, isEnabled) {
    // 绘制按钮渐变背景
    const gradient = ctx.createLinearGradient(x, y, x, y + size)
    gradient.addColorStop(0, primaryColor)
    gradient.addColorStop(1, secondaryColor)
    
    ctx.fillStyle = gradient
    this.drawRoundedRect(ctx, x, y, size, size, 8)
    ctx.fill()
    
    // 绘制按钮边框
    if (isEnabled) {
      ctx.strokeStyle = primaryColor
      ctx.lineWidth = 2
    } else {
      ctx.strokeStyle = '#BBBBBB'
      ctx.lineWidth = 1
    }
    this.drawRoundedRect(ctx, x, y, size, size, 8)
    ctx.stroke()
    
    // 添加高光效果
    if (isEnabled) {
      const highlightGradient = ctx.createLinearGradient(x, y, x, y + size / 2)
      highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)')
      highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')
      
      ctx.fillStyle = highlightGradient
      this.drawRoundedRect(ctx, x, y, size, size / 2, 8)
      ctx.fill()
    }
    
    // 绘制按钮文字
    ctx.fillStyle = isEnabled ? '#FFFFFF' : '#888888'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, x + size / 2, y + size / 2)
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  /**
   * 绘制星星评价
   */
  drawStars(ctx, centerX, y, starCount) {
    const starSize = 8
    const spacing = 16
    const totalWidth = starCount * starSize + (starCount - 1) * spacing
    const startX = centerX - totalWidth / 2
    
    ctx.save()
    for (let i = 0; i < 3; i++) {
      const x = startX + i * spacing
      if (i < starCount) {
        ctx.fillStyle = '#FFD700' // 金色星星
      } else {
        ctx.fillStyle = '#CCCCCC' // 灰色星星
      }
      this.drawStar(ctx, x, y, starSize, 5)
    }
    ctx.restore()
  }

  /**
   * 启用分享菜单
   */
  enableShareMenu() {
    if (typeof wx !== 'undefined') {
      try {
        // 显式启用分享菜单
        if (wx.showShareMenu) {
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          })
          logger.info('🎯 分享菜单已启用：转发好友、分享朋友圈')
        }

        // 启用分享到朋友圈（如果支持）
        if (wx.showShareTimelineMenu) {
          wx.showShareTimelineMenu({
            success: () => {
              logger.info('✅ 朋友圈分享菜单已启用')
            },
            fail: (err) => {
              logger.warn('⚠️ 朋友圈分享菜单启用失败:', err)
            }
          })
        }

        // 启用收藏功能（如果支持）
        if (wx.showFavoriteGuide) {
          wx.showFavoriteGuide({
            type: 'bar',
            content: '一键收藏，快速进入小游戏',
            success: () => {
              logger.info('✅ 收藏引导已显示')
            }
          })
        }

        // 检查分享菜单API可用性
        logger.info('🔍 分享API检查:')
        logger.info('- wx.showShareMenu:', typeof wx.showShareMenu)
        logger.info('- wx.onShareAppMessage:', typeof wx.onShareAppMessage)
        logger.info('- wx.shareAppMessage:', typeof wx.shareAppMessage)
        logger.info('- wx.onShareTimeline:', typeof wx.onShareTimeline)
        logger.info('- wx.shareTimeline:', typeof wx.shareTimeline)

      } catch (error) {
        logger.error('❌ 启用分享菜单失败:', error)
      }
    }
  }

  /**
   * 设置微信小游戏生命周期监听
   */
  setupWechatLifeCycle() {
    // 游戏进入后台
    wx.onHide(() => {
      logger.info('游戏进入后台')
      if (this.gameState === 'playing') {
        this.setState('paused')
      }
    })

    // 游戏回到前台
    wx.onShow(() => {
      logger.info('游戏回到前台')
      if (this.gameState === 'paused') {
        this.setState('playing')
      }
    })

    // 显式启用分享菜单（重复调用确保生效）
    if (wx.showShareMenu) {
      // 第一次调用
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline'],
        success: () => {
          logger.info('✅ 第一次分享菜单启用成功')
        },
        fail: (err) => {
          logger.error('❌ 第一次分享菜单启用失败:', err)
        }
      })
      
      // 延时再次调用确保生效
      setTimeout(() => {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline'],
          success: () => {
            logger.info('✅ 第二次分享菜单启用成功')
          },
          fail: (err) => {
            logger.warn('⚠️ 第二次分享菜单启用失败:', err)
          }
        })
      }, 1000)
    }

    // 设置被动分享（用户点击右上角菜单时）
    if (wx.onShareAppMessage) {
      wx.onShareAppMessage(() => {
        logger.info('🚀 用户通过右上角菜单转发给好友')
        return this.getShareData()
      })
    }

    // 设置朋友圈分享（如果支持）
    if (wx.onShareTimeline) {
      wx.onShareTimeline(() => {
        logger.info('📱 用户通过右上角菜单分享到朋友圈')
        return this.getTimelineShareData()
      })
    }

    // 监听复制链接（如果支持）
    if (wx.onCopyUrl) {
      wx.onCopyUrl(() => {
        logger.info('📋 用户通过右上角菜单复制链接')
        return {
          title: `鸭了个鸭呀 - 第${this.currentLevel}关`,
          path: `/?from=copy&level=${this.currentLevel}&score=${this.score}`,
          query: `level=${this.currentLevel}&score=${this.score}`
        }
      })
    }
  }

  /**
   * 获取分享数据
   * @returns {Object} 分享数据
   */
  getShareData() {
    // 根据游戏状态生成不同的分享文案
    let title, desc
    
    if (this.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀中通过了第${this.currentLevel}关！`
      desc = `得分${this.score}分，用了${this.moves}步！你能超越我吗？`
    } else if (this.gameState === 'lose') {
      title = `💪 我在鸭了个鸭呀中挑战第${this.currentLevel}关！`
      desc = `虽然失败了，但是很好玩！快来试试能不能比我厉害！`
    } else {
      title = `🦆 我正在玩鸭了个鸭呀，已经到第${this.currentLevel}关了！`
      desc = `超好玩的消除游戏，快来一起挑战吧！`
    }
    
    return {
      title: title,
      desc: desc,
      query: `from=friend&level=${this.currentLevel}&score=${this.score}`,
      // imageUrl: 'path/to/share-image.jpg', // 可以添加自定义分享图片
    }
  }

  /**
   * 获取朋友圈分享数据
   * @returns {Object} 朋友圈分享数据
   */
  getTimelineShareData() {
    let title
    
    if (this.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀游戏中通过了第${this.currentLevel}关，得分${this.score}分！你敢来挑战吗？`
    } else if (this.gameState === 'lose') {
      title = `💪 鸭了个鸭呀第${this.currentLevel}关太难了！有人能帮我通关吗？`
    } else {
      title = `🦆 我在鸭了个鸭呀游戏中玩到第${this.currentLevel}关了！来挑战看看你能玩到第几关？`
    }
    
    return {
      title: title,
      query: `from=timeline&level=${this.currentLevel}&score=${this.score}`,
      // imageUrl: 'path/to/timeline-image.jpg', // 可以添加自定义分享图片
    }
  }

  /**
   * 设置触摸事件监听
   */
  setupTouchEvents() {
    // 触摸开始事件
    wx.onTouchStart((event) => {
      // 如果分享对话框可见，允许处理点击事件（即使游戏交互被禁用）
      if (!this.isInteractionEnabled && !(this.shareDialog && this.shareDialog.visible)) {
        return
      }
      
      const touch = event.touches[0]
      if (touch) {
        this.handleTouch(touch.clientX, touch.clientY)
      }
    })

    // 触摸结束事件（可选，用于某些特殊交互）
    wx.onTouchEnd((event) => {
      // 可以在这里处理触摸结束的逻辑
    })
  }

  /**
   * 处理触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleTouch(x, y) {
    const now = Date.now()
    
    // 防抖处理
    if (now - this.lastTouchTime < this.touchDebounceTime) {
      return
    }
    this.lastTouchTime = now

    // 根据游戏状态处理不同的触摸事件
    switch (this.gameState) {
      case 'menu':
        this.handleMenuTouch(x, y)
        break
      case 'levelSelect':
        this.handleLevelSelectTouch(x, y)
        break
      case 'playing':
        this.handleGameTouch(x, y)
        break
      case 'win':
      case 'lose':
        this.handleEndGameTouch(x, y)
        break
    }
  }

  /**
   * 处理菜单界面的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleMenuTouch(x, y) {
    // 点击任意位置进入关卡选择
    logger.info('进入关卡选择')
    this.setState('levelSelect')
  }

  /**
   * 处理关卡选择界面的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleLevelSelectTouch(x, y) {
    const centerX = this.canvasSize.screenWidth / 2
    const maxLevel = this.saveManager.getMaxLevel()
    const totalLevels = 20 // 游戏总共有20关，无论是否解锁都要显示



    // 关卡按钮区域 (4列布局，更新尺寸) - 更新起始Y坐标与renderLevelGrid保持一致
    const cols = 4
    const buttonSize = 60
    const spacing = 20
    const startY = 170 // 与renderLevelGrid中的startY保持一致
    const totalWidth = cols * buttonSize + (cols - 1) * spacing
    const startX = centerX - totalWidth / 2
    
    // 计算可显示的行数
    const availableHeight = this.canvasSize.screenHeight - startY - 80
    const maxRows = Math.floor(availableHeight / (buttonSize + spacing + 35)) // 35是星星和文字的空间
    const levelsPerPage = cols * maxRows
    
    // 简单分页
    const currentPage = 0
    const startLevel = currentPage * levelsPerPage + 1
    const endLevel = Math.min(startLevel + levelsPerPage - 1, totalLevels)
    
    for (let i = startLevel; i <= endLevel; i++) {
      const index = i - startLevel
      const row = Math.floor(index / cols)
      const col = index % cols
      const buttonX = startX + col * (buttonSize + spacing)
      const buttonY = startY + row * (buttonSize + spacing + 40)
      
      if (x >= buttonX && x <= buttonX + buttonSize &&
          y >= buttonY && y <= buttonY + buttonSize) {
        
        // 检查关卡是否已解锁
        const isUnlocked = this.saveManager.isLevelUnlocked(i)
        
        if (isUnlocked) {
          logger.info(`选择关卡 ${i}`)
          this.startLevel(i)
        } else {
          logger.info(`关卡 ${i} 尚未解锁`)
          // 可以播放一个提示音效
          this.audioManager.playSound('error')
        }
        return
      }
    }
  }

  /**
   * 处理游戏中的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleGameTouch(x, y) {
    // 最优先处理分享对话框的点击（如果对话框是可见的）
    if (this.shareDialog && this.shareDialog.visible) {
      console.log(`检测到分享对话框可见，处理点击事件`)
      const action = this.shareDialog.handleClick(x, y)
      console.log(`分享对话框点击结果: ${action}`)
      
      if (action === 'confirm') {
        // 用户确认分享
        console.log(`用户确认分享`)
        this.powerUpManager.handleShareConfirm()
        return
      } else if (action === 'cancel') {
        // 用户取消分享
        console.log(`用户取消分享`)
        this.shareDialog.hide()
        // 恢复游戏交互
        this.isInteractionEnabled = true
        return
      }
      // 如果点击了对话框区域但不是按钮，不处理其他点击
      console.log(`点击了对话框区域但不是按钮，阻止其他点击`)
      return
    }
    
    // 优先处理设置面板的点击（如果面板是可见的）
    if (this.settingsPanel && this.settingsPanel.handleClick(x, y)) {
      return
    }
    
    // 检查设置按钮的点击（左上角）
    if (this.settingsButton && this.settingsButton.handleClick(x, y)) {
      return
    }
    
    // 更新可点击状态，确保被遮挡的方块为不可点击
    this.board.updateClickableStates()

    
    // 先检查是否点击了道具按钮
    for (let button of this.powerUpButtons) {
      if (button.handleClick(x, y)) {
        return // 如果点击了按钮就不继续处理方块点击
      }
    }

    // 检查是否点击了方块
    const clickedBlock = this.board.getClickedBlock(x, y)
    
    logger.info(`触摸位置（逻辑坐标）: ${x}, ${y}`)
    
    if (clickedBlock) {
      
      if (clickedBlock.isClickable) {
        this.handleBlockClick(clickedBlock)
      } else {
        logger.info('方块不可点击（被其他方块遮挡）')
        // 可以播放一个提示音效
        this.audioManager.playSound('error')
      }
    } else {
      logger.info('没有点击到任何方块')
    }
  }

  /**
   * 处理游戏结束界面的触摸
   * @param {number} x - 触摸X坐标  
   * @param {number} y - 触摸Y坐标
   */
  handleEndGameTouch(x, y) {
    if (this.gameState === 'win') {
      // 检查是否点击了胜利页面的按钮
      if (this.winScreen && this.winScreen.isButtonClicked(x, y)) {
        // 点击了继续按钮，进入下一关
        this.winScreen.hide()
        this.startLevel(this.currentLevel + 1)
      } else if (this.winScreen && this.winScreen.canSkip) {
        // 点击其他区域跳过动画
        this.winScreen.skipAnimation()
      }
    } else {
      // 失败后重新开始当前关卡
      this.startLevel(this.currentLevel)
    }
  }

  /**
   * 根据游戏进度决定初始界面
   */
  setInitialState() {
    if (this.isFirstPlay) {
      // 首次游戏，显示菜单
      this.setState('menu')
    } else {
      // 返回玩家，直接进入关卡选择
      this.setState('levelSelect')
    }
  }

  /**
   * 使用道具
   * @param {string} type - 道具类型 ('undo', 'shuffle')
   */
  usePowerUp(type) {
    if (this.gameState !== 'playing') return
    
    // 如果道具次数已用完，显示分享对话框
    if (this.powerUps[type] <= 0) {
      logger.info(`🎯 道具${type}次数已用完，显示分享对话框`)
      this.showShareDialog(type)
      return
    }

    logger.info(`🎯 尝试使用道具: ${type}，剩余次数: ${this.powerUps[type]}`)

    // 播放道具音效
    this.audioManager.playSound('powerup')

    let success = false  // 道具使用是否成功
    switch (type) {
      case 'undo':
        success = this.usePowerUpUndo()
        break
      case 'shuffle':
        success = this.usePowerUpShuffle()
        break
    }

    // 只有在道具使用成功时才减少次数
    if (success) {
      this.powerUps[type]--
      logger.info(`✅ 道具${type}使用成功，剩余次数: ${this.powerUps[type]}`)
      this.powerUpManager.updatePowerUpButtons()
    } else {
      logger.info(`❌ 道具${type}使用失败，次数未扣除，剩余次数: ${this.powerUps[type]}`)
    }
  }



  /**
   * 使用撤回道具 - 撤回最后一次移动
   * @returns {boolean} 是否成功执行撤回操作
   */
  usePowerUpUndo() {
    if (this.moveHistory.length === 0) {
      logger.info('🚫 没有可撤回的移动，无法使用撤回道具')
      return false  // 返回失败状态
    }

    const lastMove = this.moveHistory.pop()
    logger.info('↩️ 撤回最后一次移动')

    // 从槽位移除方块
    this.slot.removeBlock(lastMove.block)
    
    // 重置方块状态，清除动画残留
    const block = lastMove.block
    logger.info(`撤回前尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)
    block.reset()

    // 恢复方块的原始大小和位置
    lastMove.block.setPosition(lastMove.originalX, lastMove.originalY)
    lastMove.block.layer = lastMove.originalLayer
    block.width = lastMove.originalWidth   // 恢复原始宽度
    block.height = lastMove.originalHeight // 恢复原始高度
    logger.info(`撤回后尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)

    // 重新添加到游戏板
    this.board.blocks.push(lastMove.block)
    this.board.updateLayers()
    this.board.updateClickableStates()

    // 减少移动次数
    this.moves--
    
    logger.info(`✅ 撤回完成: 方块大小恢复为 ${lastMove.originalWidth}x${lastMove.originalHeight}`)
    return true  // 返回成功状态
  }

  /**
   * 使用洗牌道具 - 重新随机排列方块位置
   * @returns {boolean} 是否成功执行洗牌操作
   */
  usePowerUpShuffle() {
    logger.info('🔀 执行洗牌操作')
    
    // 调用游戏板的洗牌方法
    this.board.shuffle()
    
    logger.info('✅ 洗牌完成')
    return true  // 返回成功状态
  }

  /**
   * 更新道具按钮显示
   */
  updatePowerUpButtons() {
    this.powerUpButtons.forEach((button, index) => {
      const types = ['undo', 'shuffle']
      const type = types[index]
      const count = this.powerUps[type]
      
      // 更新按钮文字
      const labels = ['撤回', '洗牌']
      button.setText(`${labels[index]}(${count})`)
      
      // 按钮始终可以点击，次数为0时点击会弹出分享对话框
      button.setEnabled(true)
      
      // 如果次数为0，可以改变按钮的视觉样式来提示用户
      if (count <= 0) {
        button.setTheme(index === 0 ? 'orange' : 'green') // 改变颜色提示可以分享获得
      } else {
        button.setTheme(index === 0 ? 'blue' : 'orange') // 恢复原来的颜色
      }
    })
  }

  /**
   * 显示临时消息
   * @param {string} message - 要显示的消息
   * @param {number} duration - 显示时长（毫秒）
   */
  showMessage(message, duration = 2000) {
    this.temporaryMessage = {
      text: message,
      startTime: Date.now(),
      duration: duration
    }
    
    // 自动清除消息
    setTimeout(() => {
      if (this.temporaryMessage && this.temporaryMessage.text === message) {
        this.temporaryMessage = null
      }
    }, duration)
  }
}

// CommonJS导出
module.exports = Main