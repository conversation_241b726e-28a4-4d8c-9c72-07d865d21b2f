const logger = require('../utils/AsyncLogger')

/**
 * 道具管理器
 * 负责管理道具系统，包括道具使用、次数管理和移动历史记录
 */
class PowerUpManager {
  constructor(main) {
    this.main = main
  }

  /**
   * 使用道具
   * @param {string} type - 道具类型 ('undo', 'shuffle')
   */
  usePowerUp(type) {
    if (this.main.gameState !== 'playing') return
    
    // 如果道具次数已用完，显示分享对话框
    if (this.main.powerUps[type] <= 0) {
      logger.info(`🎯 道具${type}次数已用完，显示分享对话框`)
      this.main.shareManager.showShareDialog(type)
      return
    }

    logger.info(`🎯 尝试使用道具: ${type}，剩余次数: ${this.main.powerUps[type]}`)

    // 播放道具音效
    this.main.audioManager.playSound('powerup')

    let success = false  // 道具使用是否成功
    switch (type) {
      case 'undo':
        success = this.usePowerUpUndo()
        break
      case 'shuffle':
        success = this.usePowerUpShuffle()
        break
    }

    // 只有在道具使用成功时才减少次数
    if (success) {
      this.main.powerUps[type]--
      logger.info(`✅ 道具${type}使用成功，剩余次数: ${this.main.powerUps[type]}`)
      this.updatePowerUpButtons()
    } else {
      logger.info(`❌ 道具${type}使用失败，次数未扣除，剩余次数: ${this.main.powerUps[type]}`)
    }
  }

  /**
   * 使用撤回道具 - 撤回最后一次移动
   * @returns {boolean} 是否成功执行撤回操作
   */
  usePowerUpUndo() {
    if (this.main.moveHistory.length === 0) {
      logger.info('🚫 没有可撤回的移动，无法使用撤回道具')
      return false  // 返回失败状态
    }

    const lastMove = this.main.moveHistory.pop()
    logger.info('↩️ 撤回最后一次移动')

    // 从槽位移除方块
    this.main.slot.removeBlock(lastMove.block)
    
    // 重置方块状态，清除动画残留
    const block = lastMove.block
    logger.info(`撤回前尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)
    block.reset()

    // 恢复方块的原始大小和位置
    lastMove.block.setPosition(lastMove.originalX, lastMove.originalY)
    lastMove.block.layer = lastMove.originalLayer
    block.width = lastMove.originalWidth   // 恢复原始宽度
    block.height = lastMove.originalHeight // 恢复原始高度
    logger.info(`撤回后尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)

    // 重新添加到游戏板
    this.main.board.blocks.push(lastMove.block)
    this.main.board.updateLayers()
    this.main.board.updateClickableStates()

    // 减少移动次数
    this.main.moves--
    
    logger.info(`✅ 撤回完成: 方块大小恢复为 ${lastMove.originalWidth}x${lastMove.originalHeight}`)
    return true  // 返回成功状态
  }

  /**
   * 使用洗牌道具 - 重新随机排列方块位置
   * @returns {boolean} 是否成功执行洗牌操作
   */
  usePowerUpShuffle() {
    logger.info('🔀 执行洗牌操作')
    
    // 调用游戏板的洗牌方法
    this.main.board.shuffle()
    
    logger.info('✅ 洗牌完成')
    return true  // 返回成功状态
  }

  /**
   * 更新道具按钮显示
   */
  updatePowerUpButtons() {
    this.main.powerUpButtons.forEach((button, index) => {
      const types = ['undo', 'shuffle']
      const type = types[index]
      const count = this.main.powerUps[type]
      
      // 更新按钮文字
      const labels = ['撤回', '洗牌']
      button.setText(`${labels[index]}(${count})`)
      
      // 按钮始终可以点击，次数为0时点击会弹出分享对话框
      button.setEnabled(true)
      
      // 如果次数为0，可以改变按钮的视觉样式来提示用户
      if (count <= 0) {
        button.setTheme(index === 0 ? 'orange' : 'green') // 改变颜色提示可以分享获得
      } else {
        button.setTheme(index === 0 ? 'blue' : 'orange') // 恢复原来的颜色
      }
    })
  }

  /**
   * 记录移动历史
   * @param {Object} block - 被移动的方块
   */
  recordMove(block) {
    const moveRecord = {
      block: block,
      originalX: block.x,
      originalY: block.y,
      originalLayer: block.layer,
      originalWidth: block.initialWidth,
      originalHeight: block.initialHeight,
      timestamp: Date.now()
    }
    
    this.main.moveHistory.push(moveRecord)
    
    // 限制历史记录数量
    if (this.main.moveHistory.length > 10) {
      this.main.moveHistory.shift()
    }
  }
}

module.exports = PowerUpManager
