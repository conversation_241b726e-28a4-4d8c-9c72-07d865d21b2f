const UtilityRenderer = require('./UtilityRenderer.js')

/**
 * 游戏UI渲染器
 * 负责游戏界面UI元素的渲染，包括顶部信息栏、分数、移动次数、水印等
 */
class GameUIRenderer {
  /**
   * 构造函数
   * @param {Object} main - 主游戏实例
   */
  constructor(main) {
    this.main = main
  }

  /**
   * 渲染游戏UI
   */
  renderGameUI() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.001

    // 渲染增强版顶部信息栏
    this.renderEnhancedTopBar(ctx, time)

    // 渲染游戏状态信息
    this.renderGameStatusInfo(ctx, time)
  }

  /**
   * 渲染增强版顶部信息栏
   */
  renderEnhancedTopBar(ctx, time) {
    const barHeight = 100  // 从70增加到100，扩大标签栏高度

    // 顶部信息栏渐变背景
    const topGradient = ctx.createLinearGradient(0, 0, 0, barHeight)
    topGradient.addColorStop(0, 'rgba(74, 144, 226, 0.9)')
    topGradient.addColorStop(0.5, 'rgba(80, 227, 194, 0.8)')
    topGradient.addColorStop(1, 'rgba(184, 233, 134, 0.7)')

    ctx.fillStyle = topGradient
    ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, barHeight)

    // 添加装饰光效
    ctx.globalAlpha = 0.3
    for (let i = 0; i < 3; i++) {
      const x = (i + 1) * this.main.canvasSize.screenWidth / 4 + Math.sin(time + i * 2) * 20
      const y = barHeight / 2
      const sparkleGradient = ctx.createRadialGradient(x, y, 0, x, y, 15)
      sparkleGradient.addColorStop(0, '#FFFFFF')
      sparkleGradient.addColorStop(1, 'transparent')

      ctx.fillStyle = sparkleGradient
      ctx.beginPath()
      ctx.arc(x, y, 10 + Math.sin(time * 3 + i) * 3, 0, Math.PI * 2)
      ctx.fill()
    }
    ctx.globalAlpha = 1

    // 底部边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(0, barHeight)
    ctx.lineTo(this.main.canvasSize.screenWidth, barHeight)
    ctx.stroke()
  }

  /**
   * 渲染游戏状态信息
   */
  renderGameStatusInfo(ctx, time) {
    const centerX = this.main.canvasSize.screenWidth / 2
    const barHeight = 100  // 更新为新的标签栏高度

    // 关卡信息（右上角）
    this.renderLevelInfo(ctx, time)

    // 分数信息（居中，稍微往下调整）
    this.renderScoreInfo(ctx, centerX, barHeight / 2 + 5, time)  // 调整分数位置

    // 移动次数（左上角）
    this.renderMovesInfo(ctx, time)
  }

  /**
   * 渲染关卡信息
   */
  renderLevelInfo(ctx, time) {
    const x = this.main.canvasSize.screenWidth - 25
    const y = 50  // 从35增加到50，往下移动15px

    // 关卡信息背景
    const bgWidth = 100
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    UtilityRenderer.drawRoundedRect(ctx, x - bgWidth, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 关卡文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`第${this.main.currentLevel}关`, x - 10, y)
    ctx.restore()
  }

  /**
   * 渲染分数信息
   */
  renderScoreInfo(ctx, centerX, centerY, time) {
    // 分数背景
    const bgWidth = 120
    const bgHeight = 35

    // 脉冲效果
    const pulseScale = 1 + Math.sin(time * 4) * 0.05

    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.scale(pulseScale, pulseScale)

    // 分数背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.25)'
    UtilityRenderer.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.fill()

    // 分数边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'
    ctx.lineWidth = 2
    UtilityRenderer.drawRoundedRect(ctx, -bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 18)
    ctx.stroke()

    // 分数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.7)'
    ctx.shadowBlur = 4
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`💎 ${this.main.score}`, 0, 0)

    ctx.restore()
  }

  /**
   * 渲染移动次数信息
   */
  renderMovesInfo(ctx, time) {
    const x = 25
    const y = 35

    // 移动次数背景
    const bgWidth = 80
    const bgHeight = 30

    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    UtilityRenderer.drawRoundedRect(ctx, x, y - bgHeight / 2, bgWidth, bgHeight, 15)
    ctx.fill()

    // 移动次数文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'

    // 添加文字阴影
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    ctx.fillText(`🎯 ${this.main.moves}`, x + 10, y)
    ctx.restore()
  }

  /**
   * 渲染水印
   */
  renderWatermark() {
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2

    // 计算水印位置（游戏板下方，道具按钮上方的空白区域）
    const gameboardBottom = 80 + this.main.canvasSize.screenHeight * 0.5 // 游戏板底部
    const slotTop = this.main.canvasSize.screenHeight - 70 - 70 // 槽位顶部
    const watermarkY = gameboardBottom + (slotTop - gameboardBottom) / 2 // 中间位置

    // 保存当前状态
    ctx.save()

    // 设置透明度，让水印不太突兀
    ctx.globalAlpha = 0.6

    // 创建渐变文字效果
    const gradient = ctx.createLinearGradient(centerX - 100, watermarkY - 20, centerX + 100, watermarkY + 20)
    gradient.addColorStop(0, '#FF6B9D')    // 粉色
    gradient.addColorStop(0.3, '#FFD700')  // 金色
    gradient.addColorStop(0.6, '#FF8FB3')  // 浅粉色
    gradient.addColorStop(1, '#FFA500')    // 橙色

    // 绘制水印文字
    ctx.fillStyle = gradient
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.lineWidth = 2
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 先绘制描边
    ctx.strokeText('鸭了个鸭呀', centerX, watermarkY)
    // 再绘制填充
    ctx.fillText('鸭了个鸭呀', centerX, watermarkY)

    // 添加一些装饰效果
    const time = Date.now() * 0.003

    // 绘制小星星装饰
    ctx.globalAlpha = 0.4
    for (let i = 0; i < 3; i++) {
      const offsetX = Math.sin(time + i * 2) * 80
      const offsetY = Math.cos(time * 0.5 + i) * 10
      const starX = centerX + offsetX
      const starY = watermarkY + offsetY
      const starSize = 4 + Math.sin(time * 2 + i) * 2

      ctx.fillStyle = '#FFD700'
      UtilityRenderer.drawStar(ctx, starX, starY, starSize, 5)
    }

    // 恢复状态
    ctx.restore()
  }
}

// CommonJS导出
module.exports = GameUIRenderer
