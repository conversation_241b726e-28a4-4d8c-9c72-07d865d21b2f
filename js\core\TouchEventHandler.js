const logger = require('../utils/AsyncLogger')

/**
 * 触摸事件处理器
 * 负责处理游戏中的所有触摸事件，包括菜单、关卡选择、游戏中的触摸交互
 */
class TouchEventHandler {
  constructor(main) {
    this.main = main
  }

  /**
   * 设置触摸事件监听
   */
  setupTouchEvents() {
    // 触摸开始事件
    wx.onTouchStart((event) => {
      // 如果分享对话框可见，允许处理点击事件（即使游戏交互被禁用）
      if (!this.main.isInteractionEnabled && !(this.main.shareDialog && this.main.shareDialog.visible)) {
        return
      }
      
      const touch = event.touches[0]
      if (touch) {
        this.handleTouch(touch.clientX, touch.clientY)
      }
    })

    // 触摸结束事件（可选，用于某些特殊交互）
    wx.onTouchEnd((event) => {
      // 可以在这里处理触摸结束的逻辑
    })
  }

  /**
   * 处理触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleTouch(x, y) {
    const now = Date.now()
    
    // 防抖处理
    if (now - this.main.lastTouchTime < this.main.touchDebounceTime) {
      return
    }
    this.main.lastTouchTime = now

    // 根据游戏状态处理不同的触摸事件
    switch (this.main.gameState) {
      case 'menu':
        this.handleMenuTouch(x, y)
        break
      case 'levelSelect':
        this.handleLevelSelectTouch(x, y)
        break
      case 'playing':
        this.handleGameTouch(x, y)
        break
      case 'win':
      case 'lose':
        this.handleEndGameTouch(x, y)
        break
    }
  }

  /**
   * 处理菜单界面的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleMenuTouch(x, y) {
    // 点击任意位置进入关卡选择
    logger.info('进入关卡选择')
    this.main.setState('levelSelect')
  }

  /**
   * 处理关卡选择界面的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleLevelSelectTouch(x, y) {
    const centerX = this.main.canvasSize.screenWidth / 2
    const maxLevel = this.main.saveManager.getMaxLevel()
    const totalLevels = 20 // 游戏总共有20关，无论是否解锁都要显示

    // 关卡按钮区域 (4列布局) - 与LevelSelectRenderer.js保持完全一致
    const cols = 4
    const buttonSize = 60
    const spacing = 20
    const startY = 170 // 与LevelSelectRenderer中的startY保持一致
    const totalWidth = cols * buttonSize + (cols - 1) * spacing
    const startX = centerX - totalWidth / 2

    // 计算可显示的行数
    const availableHeight = this.main.canvasSize.screenHeight - startY - 80
    const maxRows = Math.floor(availableHeight / (buttonSize + spacing + 35)) // 35是星星和文字的空间
    const levelsPerPage = cols * maxRows

    // 简单分页
    const currentPage = 0
    const startLevel = currentPage * levelsPerPage + 1
    const endLevel = Math.min(startLevel + levelsPerPage - 1, totalLevels)

    for (let i = startLevel; i <= endLevel; i++) {
      const index = i - startLevel
      const row = Math.floor(index / cols)
      const col = index % cols
      const buttonX = startX + col * (buttonSize + spacing)
      const buttonY = startY + row * (buttonSize + spacing + 40) // 与LevelSelectRenderer保持一致

      if (x >= buttonX && x <= buttonX + buttonSize &&
          y >= buttonY && y <= buttonY + buttonSize) {

        // 检查关卡是否已解锁
        const isUnlocked = this.main.saveManager.isLevelUnlocked(i)

        if (isUnlocked) {
          logger.info(`选择关卡 ${i}`)
          this.main.startLevel(i)
        } else {
          logger.info(`关卡 ${i} 尚未解锁`)
          // 可以播放一个提示音效
          this.main.audioManager.playSound('error')
        }
        return
      }
    }
  }

  /**
   * 处理游戏中的触摸
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleGameTouch(x, y) {
    // 最优先处理分享对话框的点击（如果对话框是可见的）
    if (this.main.shareDialog && this.main.shareDialog.visible) {
      console.log(`检测到分享对话框可见，处理点击事件`)
      const action = this.main.shareDialog.handleClick(x, y)
      console.log(`分享对话框点击结果: ${action}`)
      
      if (action === 'confirm') {
        // 用户确认分享
        console.log(`用户确认分享`)
        this.main.shareManager.handleShareConfirm()
        return
      } else if (action === 'cancel') {
        // 用户取消分享
        console.log(`用户取消分享`)
        this.main.shareDialog.hide()
        // 恢复游戏交互
        this.main.isInteractionEnabled = true
        return
      }
      // 如果点击了对话框区域但不是按钮，不处理其他点击
      console.log(`点击了对话框区域但不是按钮，阻止其他点击`)
      return
    }
    
    // 优先处理设置面板的点击（如果面板是可见的）
    if (this.main.settingsPanel && this.main.settingsPanel.handleClick(x, y)) {
      return
    }
    
    // 检查设置按钮的点击（左上角）
    if (this.main.settingsButton && this.main.settingsButton.handleClick(x, y)) {
      return
    }
    
    // 更新可点击状态，确保被遮挡的方块为不可点击
    this.main.board.updateClickableStates()

    
    // 先检查是否点击了道具按钮
    for (let button of this.main.powerUpButtons) {
      if (button.handleClick(x, y)) {
        return // 如果点击了按钮就不继续处理方块点击
      }
    }

    // 检查是否点击了方块
    const clickedBlock = this.main.board.getClickedBlock(x, y)
    
    logger.info(`触摸位置（逻辑坐标）: ${x}, ${y}`)
    
    if (clickedBlock) {
      
      if (clickedBlock.isClickable) {
        this.main.handleBlockClick(clickedBlock)
      } else {
        logger.info('方块不可点击（被其他方块遮挡）')
        // 可以播放一个提示音效
        this.main.audioManager.playSound('error')
      }
    } else {
      logger.info('没有点击到任何方块')
    }
  }

  /**
   * 处理游戏结束界面的触摸
   * @param {number} x - 触摸X坐标  
   * @param {number} y - 触摸Y坐标
   */
  handleEndGameTouch(x, y) {
    if (this.main.gameState === 'win') {
      // 检查是否点击了胜利页面的按钮
      if (this.main.winScreen && this.main.winScreen.isButtonClicked(x, y)) {
        // 点击了继续按钮，进入下一关
        this.main.winScreen.hide()
        this.main.startLevel(this.main.currentLevel + 1)
      } else if (this.main.winScreen && this.main.winScreen.canSkip) {
        // 点击其他区域跳过动画
        this.main.winScreen.skipAnimation()
      }
    } else {
      // 失败后重新开始当前关卡
      this.main.startLevel(this.main.currentLevel)
    }
  }
}

module.exports = TouchEventHandler
