const logger = require('../utils/AsyncLogger')

/**
 * 游戏循环管理器
 * 负责管理游戏主循环、帧率控制和游戏对象更新
 */
class GameLoopManager {
  constructor(main) {
    this.main = main
    this.DEBUG_MODE = false
    this.isRunning = false
    this.lastTime = 0
  }

  /**
   * 开始游戏循环
   */
  startGameLoop() {
    if (this.isRunning) {
      logger.warn('游戏循环已在运行中')
      return
    }

    this.isRunning = true
    this.lastTime = 0
    
    const gameLoop = (timestamp) => {
      if (!this.isRunning) return

      const deltaTime = timestamp - this.lastTime
      this.lastTime = timestamp
      
      // 更新游戏状态
      this.update(deltaTime)
      
      // 渲染游戏画面
      this.render()
      
      // 请求下一帧
      requestAnimationFrame(gameLoop)
    }
    
    requestAnimationFrame(gameLoop)
    if (this.DEBUG_MODE) logger.info('游戏循环已启动')
  }

  /**
   * 停止游戏循环
   */
  stopGameLoop() {
    this.isRunning = false
    if (this.DEBUG_MODE) logger.info('游戏循环已停止')
  }

  /**
   * 更新游戏状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新动画管理器
    this.main.animationManager.update(Date.now())

    // 更新消息管理器
    if (this.main.messageManager) {
      this.main.messageManager.updateMessages()
    }

    // 更新文件信息面板
    if (this.main.fileInfoPanel) {
      this.main.fileInfoPanel.update(deltaTime)
    }

    // 更新设置UI
    if (this.main.settingsButton) {
      this.main.settingsButton.update(deltaTime)
    }
    if (this.main.settingsPanel) {
      this.main.settingsPanel.update(deltaTime)
    }

    // 更新分享对话框
    if (this.main.shareDialog) {
      this.main.shareDialog.update(deltaTime)
    }

    // 更新胜利页面
    if (this.main.winScreen && this.main.gameState === 'win') {
      this.main.winScreen.update(deltaTime)
    }

    // 更新游戏对象（仅在游戏进行时）
    if (this.main.gameState === 'playing') {
      this.updateGameObjects(deltaTime)
    }
  }

  /**
   * 更新游戏对象
   * @param {number} deltaTime - 时间差
   */
  updateGameObjects(deltaTime) {
    // 更新游戏板
    if (this.main.board) {
      this.main.board.update(deltaTime)
    }
    
    // 更新槽位
    if (this.main.slot) {
      this.main.slot.update(deltaTime)
    }
    
    // 更新道具按钮
    this.main.powerUpButtons.forEach(button => {
      button.update(deltaTime)
    })

    // 定期更新文件信息面板内容
    if (this.main.fileInfoPanel && Date.now() % 1000 < 50) { // 大约每秒更新一次
      this.updateFileInfoPanel()
    }
  }

  /**
   * 渲染游戏画面
   */
  render() {
    // 委托给渲染管理器
    if (this.main.renderManager) {
      this.main.renderManager.render()
    }
  }

  /**
   * 更新文件信息面板内容
   */
  updateFileInfoPanel() {
    if (!this.main.fileInfoPanel) return

    const content = [
      `游戏版本: v1.0.0`,
      `当前关卡: ${this.main.currentLevel}`,
      `移动次数: ${this.main.moves}`,
      `游戏状态: ${this.main.gameState}`,
      `道具剩余: 撤回${this.main.powerUps.undo} 洗牌${this.main.powerUps.shuffle}`,
      `屏幕尺寸: ${this.main.canvasSize.screenWidth}x${this.main.canvasSize.screenHeight}`,
      `像素比: ${this.main.canvasSize.pixelRatio}`,
      `调试模式: ${this.main.debugMode ? '开启' : '关闭'}`
    ]

    this.main.fileInfoPanel.setContent(content)
  }

  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用调试模式
   */
  setDebugMode(enabled) {
    this.DEBUG_MODE = enabled
  }

  /**
   * 获取游戏循环状态
   * @returns {boolean} 游戏循环是否正在运行
   */
  isGameLoopRunning() {
    return this.isRunning
  }
}

module.exports = GameLoopManager
