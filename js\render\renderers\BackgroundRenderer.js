const UtilityRenderer = require('./UtilityRenderer.js')

/**
 * 背景渲染器
 * 负责游戏背景、装饰元素和光效的渲染
 */
class BackgroundRenderer {
  /**
   * 构造函数
   * @param {Object} main - 主游戏实例
   */
  constructor(main) {
    this.main = main
  }

  /**
   * 绘制背景
   */
  renderBackground() {
    // 更美观的渐变背景
    const gradient = this.main.ctx.createLinearGradient(
      0, 0, 0, this.main.canvasSize.screenHeight
    )
    gradient.addColorStop(0, '#FFE4E1') // 薄雾玫瑰
    gradient.addColorStop(0.3, '#E6E6FA') // 薰衣草
    gradient.addColorStop(0.7, '#F0F8FF') // 爱丽丝蓝
    gradient.addColorStop(1, '#F5FFFA') // 薄荷奶油
    
    this.main.ctx.fillStyle = gradient
    this.main.ctx.fillRect(0, 0, this.main.canvasSize.screenWidth, this.main.canvasSize.screenHeight)
    
    // 添加动态装饰元素
    this.renderDecorations()
    
    // 添加光效
    this.renderLightEffects()
  }

  /**
   * 绘制装饰元素
   */
  renderDecorations() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.001 // 用于动画
    
    // 绘制浮动的装饰圆圈
    ctx.globalAlpha = 0.3
    for (let i = 0; i < 8; i++) {
      const x = (i * this.main.canvasSize.screenWidth / 7) + Math.sin(time + i) * 20
      const y = 100 + Math.cos(time * 0.5 + i) * 30
      const radius = 15 + Math.sin(time * 2 + i) * 5
      
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      gradient.addColorStop(0, '#FFB6C1') // 浅粉色
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }
    
    // 绘制星星装饰
    ctx.fillStyle = '#FFD700'
    ctx.globalAlpha = 0.6
    for (let i = 0; i < 5; i++) {
      const x = Math.random() * this.main.canvasSize.screenWidth
      const y = 50 + Math.random() * 100
      const size = 3 + Math.sin(time * 3 + i) * 2
      
      UtilityRenderer.drawStar(ctx, x, y, size, 5)
    }
    
    ctx.globalAlpha = 1
  }

  /**
   * 绘制光效
   */
  renderLightEffects() {
    const ctx = this.main.ctx
    const time = Date.now() * 0.002
    
    // 绘制柔和的光晕效果
    ctx.globalAlpha = 0.1
    for (let i = 0; i < 3; i++) {
      const x = this.main.canvasSize.screenWidth * (0.2 + i * 0.3)
      const y = this.main.canvasSize.screenHeight * 0.3
      const radius = 100 + Math.sin(time + i * 2) * 20
      
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
      gradient.addColorStop(0, '#FFFFFF')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()
    }
    
    ctx.globalAlpha = 1
  }
}

// CommonJS导出
module.exports = BackgroundRenderer
