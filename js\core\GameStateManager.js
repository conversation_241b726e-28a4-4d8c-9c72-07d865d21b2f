const logger = require('../utils/AsyncLogger')

/**
 * 游戏状态管理器
 * 负责管理游戏状态转换和相关的副作用处理
 */
class GameStateManager {
  constructor(main) {
    this.main = main
    this.DEBUG_MODE = false // 从main.js中提取的调试模式
  }

  /**
   * 设置游戏状态
   * @param {string} state - 新状态
   */
  setState(state) {
    if (this.DEBUG_MODE) logger.info(`游戏状态变更: ${this.main.gameState} -> ${state}`)
    this.main.gameState = state
    
    // 根据状态进行相应处理
    switch (state) {
      case 'loading':
        this.main.isInteractionEnabled = false
        break
      case 'menu':
        this.main.isInteractionEnabled = true
        break
      case 'levelSelect':
        this.main.isInteractionEnabled = true
        // 进入关卡选择时，尝试加载服务器进度（开发者模式下跳过）
        if (!this.main.saveManager.isDeveloperMode()) {
          this.main.serverProgressManager.loadServerProgress()
        } else {
          logger.info('🛠️ 开发者模式：跳过服务器进度加载')
        }
        break
      case 'playing':
        this.main.isInteractionEnabled = true
        break
      case 'paused':
        this.main.isInteractionEnabled = false
        break
      case 'win':
        this.main.isInteractionEnabled = true
        // 显示美化的胜利页面
        this.handleWinState()
        break
      case 'lose':
        this.main.isInteractionEnabled = true
        // 显示美化的失败页面
        this.handleLoseState()
        break
    }
  }

  /**
   * 处理胜利状态
   */
  handleWinState() {
    if (this.main.winScreen) {
      // 计算用时，确保不为负数
      let playTime = 0
      if (this.main.endTime > 0 && this.main.startTime > 0 && this.main.endTime >= this.main.startTime) {
        playTime = Math.floor((this.main.endTime - this.main.startTime) / 1000)
      }

      this.main.winScreen.show({
        time: playTime,
        score: this.main.score,
        moves: this.main.moves
      })
    }
  }

  /**
   * 处理失败状态
   */
  handleLoseState() {
    if (this.main.loseScreen) {
      // 计算用时，确保不为负数
      let playTime = 0
      if (this.main.endTime > 0 && this.main.startTime > 0 && this.main.endTime >= this.main.startTime) {
        playTime = Math.floor((this.main.endTime - this.main.startTime) / 1000)
      }

      this.main.loseScreen.show({
        time: playTime,
        score: this.main.score,
        moves: this.main.moves
      }, {
        onRetry: () => {
          this.main.loseScreen.hide()
          this.restartCurrentLevel()
        },
        onBack: () => {
          this.main.loseScreen.hide()
          this.backToLevelSelect()
        }
      })
    }
  }

  /**
   * 根据游戏进度决定初始界面
   */
  setInitialState() {
    if (this.main.isFirstPlay) {
      // 首次游戏，显示菜单
      this.setState('menu')
    } else {
      // 返回玩家，直接进入关卡选择
      this.setState('levelSelect')
    }
  }

  /**
   * 开始指定关卡
   * @param {number} level - 关卡编号
   */
  startLevel(level) {
    if (this.DEBUG_MODE) logger.info(`开始关卡 ${level}`)
    
    this.main.currentLevel = level
    
    // 更新全局变量用于调试（Block.js会用到）
    if (typeof window !== 'undefined') {
      window.currentLevel = level
    }
    
    this.main.score = 0
    this.main.moves = 0
    this.main.startTime = Date.now()
    this.main.endTime = 0 // 重置结束时间
    this.main.moveHistory = [] // 清空移动历史
    
    // 重置道具次数（每关重置）
    this.main.powerUps = {
      undo: 3,
      shuffle: 2
    }
    
    // 清空槽位
    this.main.slot.clear()
    
    // 初始化游戏板
    this.main.board.initLevel(level)
    
    // 更新道具按钮
    this.main.powerUpManager.updatePowerUpButtons()
    
    // 播放背景音乐
    this.main.audioManager.playBackgroundMusic()
    
    // 设置游戏状态
    this.setState('playing')
  }

  /**
   * 重新开始当前关卡
   */
  restartCurrentLevel() {
    logger.info(`🔄 重新开始关卡 ${this.main.currentLevel}`)
    
    // 重新开始当前关卡，保持关卡编号不变
    this.startLevel(this.main.currentLevel)
    
    // 显示提示信息
    this.main.messageManager.showMessage(`重新开始第${this.main.currentLevel}关`, 2000)
  }

  /**
   * 返回关卡选择
   */
  backToLevelSelect() {
    logger.info('返回关卡选择页面')
    this.setState('levelSelect')
  }
}

module.exports = GameStateManager
