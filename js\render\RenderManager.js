// 引入各个渲染器模块
const BackgroundRenderer = require('./renderers/BackgroundRenderer.js')
const MenuRenderer = require('./renderers/MenuRenderer.js')
const LevelSelectRenderer = require('./renderers/LevelSelectRenderer.js')
const GameUIRenderer = require('./renderers/GameUIRenderer.js')
const MessageRenderer = require('./renderers/MessageRenderer.js')
const DebugRenderer = require('./renderers/DebugRenderer.js')

/**
 * 渲染管理器
 * 负责游戏的所有界面渲染逻辑
 * 重构后采用模块化设计，将不同功能的渲染逻辑分离到专门的渲染器中
 */
class RenderManager {
  constructor(mainInstance) {
    this.main = mainInstance
    
    // 初始化各个渲染器模块
    this.backgroundRenderer = new BackgroundRenderer(mainInstance)
    this.menuRenderer = new MenuRenderer(mainInstance)
    this.levelSelectRenderer = new LevelSelectRenderer(mainInstance)
    this.gameUIRenderer = new GameUIRenderer(mainInstance)
    this.messageRenderer = new MessageRenderer(mainInstance)
    this.debugRenderer = new DebugRenderer(mainInstance)
  }

  /**
   * 主渲染方法
   */
  render() {
    // 清空画布
    this.main.ctx.clearRect(0, 0, this.main.canvasSize.screenWidth, this.main.canvasSize.screenHeight)
    
    // 绘制背景
    this.renderBackground()
    
    // 根据游戏状态渲染不同内容
    switch (this.main.gameState) {
      case 'loading':
        this.renderLoadingScreen()
        break
      case 'menu':
        this.renderMenuScreen()
        break
      case 'levelSelect':
        this.renderLevelSelectScreen()
        break
      case 'playing':
        this.renderGameScreen()
        break
      case 'win':
        this.renderWinScreen()
        break
      case 'lose':
        this.renderLoseScreen()
        break
    }

    // 渲染文件信息面板（显示在所有内容上方）
    if (this.main.fileInfoPanel && this.main.debugMode) {
      this.main.fileInfoPanel.render(this.main.ctx)
    }
    
    // 渲染临时消息（显示在最上层）
    this.renderTemporaryMessage()
  }

  /**
   * 渲染临时消息
   */
  renderTemporaryMessage() {
    this.messageRenderer.renderTemporaryMessage()
  }

  /**
   * 绘制背景
   */
  renderBackground() {
    this.backgroundRenderer.renderBackground()
  }

  /**
   * 渲染加载界面
   */
  renderLoadingScreen() {
    this.main.ctx.fillStyle = '#333333'
    this.main.ctx.font = '24px Arial'
    this.main.ctx.textAlign = 'center'
    this.main.ctx.fillText(
      '游戏加载中...',
      this.main.canvasSize.screenWidth / 2,
      this.main.canvasSize.screenHeight / 2
    )
  }

  /**
   * 渲染菜单界面
   */
  renderMenuScreen() {
    this.menuRenderer.renderMenuScreen()
  }

  /**
   * 渲染关卡选择界面
   */
  renderLevelSelectScreen() {
    this.levelSelectRenderer.renderLevelSelectScreen()
  }

  /**
   * 渲染游戏界面
   */
  renderGameScreen() {
    // 每帧渲染前更新可点击状态，保证状态与视觉同步
    this.main.board.updateClickableStates()
    // 渲染UI信息
    this.renderGameUI()

    // 渲染游戏板
    if (this.main.board) {
      this.main.board.render(this.main.ctx, this.main.resources)

      // 如果启用调试模式，渲染层级信息
      if (this.main.debugMode) {
        this.renderDebugInfo()
      }
    }

    // 渲染槽位
    if (this.main.slot) {
      this.main.slot.render(this.main.ctx, this.main.resources)
    }

    // 渲染水印
    this.renderWatermark()

    // 渲染道具按钮
    this.main.powerUpButtons.forEach(button => {
      button.render(this.main.ctx)
    })

    // 渲染设置按钮
    if (this.main.settingsButton) {
      this.main.settingsButton.render(this.main.ctx)
    }

    // 渲染设置面板（最上层）
    if (this.main.settingsPanel) {
      this.main.settingsPanel.render(this.main.ctx)
    }

    // 渲染分享对话框（最最上层）
    if (this.main.shareDialog && this.main.shareDialog.visible) {
      this.main.shareDialog.render(this.main.ctx)
    }
  }

  /**
   * 渲染游戏UI
   */
  renderGameUI() {
    this.gameUIRenderer.renderGameUI()
  }

  /**
   * 渲染水印
   */
  renderWatermark() {
    this.gameUIRenderer.renderWatermark()
  }

  /**
   * 渲染胜利界面
   */
  renderWinScreen() {
    // 先渲染游戏界面作为背景
    this.renderGameScreen()

    // 使用新的胜利页面组件渲染
    if (this.main.winScreen) {
      this.main.winScreen.render(this.main.ctx)
    }
  }

  /**
   * 渲染失败界面
   */
  renderLoseScreen() {
    // 先渲染游戏界面作为背景
    this.renderGameScreen()

    // 使用新的失败页面组件渲染
    if (this.main.loseScreen) {
      this.main.loseScreen.render(this.main.ctx)
    }
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    this.debugRenderer.renderDebugInfo()
  }
}

// CommonJS导出
module.exports = RenderManager
