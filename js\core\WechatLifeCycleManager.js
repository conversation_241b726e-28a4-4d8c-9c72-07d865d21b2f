const logger = require('../utils/AsyncLogger')

/**
 * 微信小游戏生命周期管理器
 * 负责管理微信小游戏的生命周期监听、分享菜单和相关API功能
 */
class WechatLifeCycleManager {
  constructor(main) {
    this.main = main
  }

  /**
   * 设置微信小游戏生命周期监听
   */
  setupWechatLifeCycle() {
    if (typeof wx === 'undefined') {
      logger.warn('微信API不可用，跳过生命周期设置')
      return
    }

    // 游戏进入后台
    wx.onHide(() => {
      logger.info('游戏进入后台')
      if (this.main.gameState === 'playing') {
        this.main.gameStateManager.setState('paused')
      }
    })

    // 游戏回到前台
    wx.onShow(() => {
      logger.info('游戏回到前台')
      if (this.main.gameState === 'paused') {
        this.main.gameStateManager.setState('playing')
      }
    })

    // 显式启用分享菜单（重复调用确保生效）
    this.enableShareMenuWithRetry()

    // 设置被动分享（用户点击右上角菜单时）
    this.setupPassiveShare()

    // 设置其他分享功能
    this.setupAdditionalShareFeatures()
  }

  /**
   * 启用分享菜单（带重试机制）
   */
  enableShareMenuWithRetry() {
    if (!wx.showShareMenu) return

    // 第一次调用
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        logger.info('✅ 第一次分享菜单启用成功')
      },
      fail: (err) => {
        logger.error('❌ 第一次分享菜单启用失败:', err)
      }
    })
    
    // 延时再次调用确保生效
    setTimeout(() => {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline'],
        success: () => {
          logger.info('✅ 第二次分享菜单启用成功')
        },
        fail: (err) => {
          logger.warn('⚠️ 第二次分享菜单启用失败:', err)
        }
      })
    }, 1000)
  }

  /**
   * 设置被动分享功能
   */
  setupPassiveShare() {
    // 设置被动分享（用户点击右上角菜单时）
    if (wx.onShareAppMessage) {
      wx.onShareAppMessage(() => {
        logger.info('🚀 用户通过右上角菜单转发给好友')
        return this.getShareData()
      })
    }

    // 设置朋友圈分享（如果支持）
    if (wx.onShareTimeline) {
      wx.onShareTimeline(() => {
        logger.info('📱 用户通过右上角菜单分享到朋友圈')
        return this.getTimelineShareData()
      })
    }

    // 监听复制链接（如果支持）
    if (wx.onCopyUrl) {
      wx.onCopyUrl(() => {
        logger.info('📋 用户通过右上角菜单复制链接')
        return {
          title: `鸭了个鸭呀 - 第${this.main.currentLevel}关`,
          path: `/?from=copy&level=${this.main.currentLevel}&score=${this.main.score}`,
          query: `level=${this.main.currentLevel}&score=${this.main.score}`
        }
      })
    }
  }

  /**
   * 设置其他分享功能
   */
  setupAdditionalShareFeatures() {
    // 启用分享到朋友圈（如果支持）
    if (wx.showShareTimelineMenu) {
      wx.showShareTimelineMenu({
        success: () => {
          logger.info('✅ 朋友圈分享菜单已启用')
        },
        fail: (err) => {
          logger.warn('⚠️ 朋友圈分享菜单启用失败:', err)
        }
      })
    }

    // 启用收藏功能（如果支持）
    if (wx.showFavoriteGuide) {
      wx.showFavoriteGuide({
        type: 'bar',
        content: '一键收藏，快速进入小游戏',
        success: () => {
          logger.info('✅ 收藏引导已显示')
        }
      })
    }

    // 检查分享菜单API可用性
    this.logShareAPIAvailability()
  }

  /**
   * 启用分享菜单
   */
  enableShareMenu() {
    if (typeof wx === 'undefined') return

    try {
      // 显式启用分享菜单
      if (wx.showShareMenu) {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        })
        logger.info('🎯 分享菜单已启用：转发好友、分享朋友圈')
      }

      this.setupAdditionalShareFeatures()

    } catch (error) {
      logger.error('❌ 启用分享菜单失败:', error)
    }
  }

  /**
   * 记录分享API可用性
   */
  logShareAPIAvailability() {
    logger.info('🔍 分享API检查:')
    logger.info('- wx.showShareMenu:', typeof wx.showShareMenu)
    logger.info('- wx.onShareAppMessage:', typeof wx.onShareAppMessage)
    logger.info('- wx.shareAppMessage:', typeof wx.shareAppMessage)
    logger.info('- wx.onShareTimeline:', typeof wx.onShareTimeline)
    logger.info('- wx.shareTimeline:', typeof wx.shareTimeline)
  }

  /**
   * 获取分享数据
   * @returns {Object} 分享数据
   */
  getShareData() {
    // 根据游戏状态生成不同的分享文案
    let title, desc
    
    if (this.main.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀中通过了第${this.main.currentLevel}关！`
      desc = `得分${this.main.score}分，用了${this.main.moves}步！你能超越我吗？`
    } else if (this.main.gameState === 'lose') {
      title = `💪 我在鸭了个鸭呀中挑战第${this.main.currentLevel}关！`
      desc = `虽然失败了，但是很好玩！快来试试能不能比我厉害！`
    } else {
      title = `🦆 我正在玩鸭了个鸭呀，已经到第${this.main.currentLevel}关了！`
      desc = `超好玩的消除游戏，快来一起挑战吧！`
    }
    
    return {
      title: title,
      desc: desc,
      query: `from=friend&level=${this.main.currentLevel}&score=${this.main.score}`,
      // imageUrl: 'path/to/share-image.jpg', // 可以添加自定义分享图片
    }
  }

  /**
   * 获取朋友圈分享数据
   * @returns {Object} 朋友圈分享数据
   */
  getTimelineShareData() {
    let title
    
    if (this.main.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀游戏中通过了第${this.main.currentLevel}关，得分${this.main.score}分！你敢来挑战吗？`
    } else if (this.main.gameState === 'lose') {
      title = `💪 鸭了个鸭呀第${this.main.currentLevel}关太难了！有人能帮我通关吗？`
    } else {
      title = `🦆 我在鸭了个鸭呀游戏中玩到第${this.main.currentLevel}关了！来挑战看看你能玩到第几关？`
    }
    
    return {
      title: title,
      query: `from=timeline&level=${this.main.currentLevel}&score=${this.main.score}`,
      // imageUrl: 'path/to/timeline-image.jpg', // 可以添加自定义分享图片
    }
  }
}

module.exports = WechatLifeCycleManager
