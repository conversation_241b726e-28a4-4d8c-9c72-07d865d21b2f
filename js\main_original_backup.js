const Utils = require('./utils/Utils.js')
const ResourceLoader = require('./utils/ResourceLoader.js')
const EventManager = require('./utils/EventManager.js')
const ImageGenerator = require('./utils/ImageGenerator.js')
const CartoonImageLoader = require('./utils/CartoonImageLoader.js')
const AnimationManager = require('./utils/AnimationManager.js')
const ErrorHandler = require('./utils/ErrorHandler.js')
const AudioManager = require('./utils/AudioManager.js')
const GameSaveManager = require('./utils/GameSaveManager.js')
const UserManager = require('./utils/UserManager.js')
const NetworkManager = require('./utils/NetworkManager.js')
const Board = require('./game/Board.js')
const Slot = require('./game/Slot.js')
const Button = require('./ui/Button.js')
const ParticleSystem = require('./effects/ParticleSystem.js')
const WinScreen = require('./ui/WinScreen.js')
const CollapsiblePanel = require('./ui/CollapsiblePanel.js')
const SettingsButton = require('./ui/SettingsButton.js')
const SettingsPanel = require('./ui/SettingsPanel.js')
const ShareDialog = require('./ui/ShareDialog.js')
const RenderManager = require('./render/RenderManager.js')
const GameInitializer = require('./core/GameInitializer.js')
const ShareManager = require('./core/ShareManager.js')
const TouchEventHandler = require('./core/TouchEventHandler.js')
const ServerProgressManager = require('./core/ServerProgressManager.js')
const UIRenderer = require('./core/UIRenderer.js')
const logger = require('./utils/AsyncLogger')


// 全局DEBUG配置 - 设为false关闭所有调试日志
const DEBUG_MODE = false

// 添加调试日志 - 现在受DEBUG_MODE控制
if (DEBUG_MODE) {
  logger.info('📦 主模块依赖加载情况:')
  logger.info('- Utils:', typeof Utils)
  logger.info('- ResourceLoader:', typeof ResourceLoader)
  logger.info('- EventManager:', typeof EventManager)
  logger.info('- ImageGenerator:', typeof ImageGenerator)
  logger.info('- CartoonImageLoader:', typeof CartoonImageLoader)
  logger.info('- AnimationManager:', typeof AnimationManager)
  logger.info('- ErrorHandler:', typeof ErrorHandler)
  logger.info('- AudioManager:', typeof AudioManager)
  logger.info('- GameSaveManager:', typeof GameSaveManager)
  logger.info('- UserManager:', typeof UserManager)
  logger.info('- NetworkManager:', typeof NetworkManager)
  logger.info('- Board:', typeof Board)
  logger.info('- Slot:', typeof Slot)
  logger.info('- Button:', typeof Button)
  logger.info('- CollapsiblePanel:', typeof CollapsiblePanel)
  logger.info('- SettingsButton:', typeof SettingsButton)
  logger.info('- SettingsPanel:', typeof SettingsPanel)
}

/**
 * 游戏主控制器
 * 管理游戏的整体流程、Canvas渲染和用户交互
 */
class Main {
  constructor() {
    this.canvas = null
    this.ctx = null
    this.resources = new ResourceLoader()
    this.eventManager = new EventManager()
    this.animationManager = new AnimationManager()
    this.errorHandler = new ErrorHandler()
    this.audioManager = new AudioManager()
    this.userManager = new UserManager()
    this.saveManager = new GameSaveManager()
    
    // 游戏状态
    this.gameState = 'loading' // loading, menu, playing, paused, win, lose
    this.currentLevel = 1
    this.score = 0
    this.moves = 0
    this.startTime = 0
    this.endTime = 0
    
    // 游戏对象
    this.board = null
    this.slot = null
    this.powerUpButtons = [] // 道具按钮
    this.fileInfoPanel = null // 文件信息面板
    this.settingsButton = null // 设置按钮
    this.settingsPanel = null // 设置面板
    this.shareDialog = null // 分享对话框
    
    // 道具使用次数
    this.powerUps = {
      undo: 3,      // 撤回次数 
      shuffle: 2    // 洗牌次数
    }
    this.moveHistory = [] // 移动历史，用于撤回
    
    // 交互状态
    this.isInteractionEnabled = true
    this.lastTouchTime = 0
    this.touchDebounceTime = 100 // 防止重复点击
    
    // 调试模式 - 关闭调试模式以提升性能
    this.debugMode = false
    
    // 调试手势检测
    this.debugGestureClicks = []
    this.debugGestureTimeout = null
    
    // 屏幕适配 - 在setupCanvas中初始化
    this.canvasSize = null
    
    // 服务器进度数据
    this.serverProgress = null
    this.isLoadingServerProgress = false

    // 初始化渲染管理器
    this.renderManager = new RenderManager(this)

    // 初始化新的模块化管理器
    this.gameInitializer = new GameInitializer(this)
    this.shareManager = new ShareManager(this)
    this.touchEventHandler = new TouchEventHandler(this)
    this.serverProgressManager = new ServerProgressManager(this)
    this.uiRenderer = new UIRenderer(this)

    this.init()
  }

  /**
   * 初始化游戏
   */
  async init() {
    if (DEBUG_MODE) logger.info('初始化游戏...')
    
    try {
      // 创建Canvas
      this.gameInitializer.setupCanvas()

      // 设置触摸事件
      this.touchEventHandler.setupTouchEvents()

      // 加载资源
      await this.gameInitializer.loadResources()

      // 初始化音频系统
      await this.audioManager.init()

      // 初始化用户系统
      await this.gameInitializer.initUserSystem()

      // 加载游戏进度
      this.gameInitializer.loadGameProgress()

      // 初始化游戏对象
      this.gameInitializer.initGameObjects()
      
      // 开始游戏循环
      this.startGameLoop()
      
      // 设置微信小游戏生命周期监听
      this.setupWechatLifeCycle()
      
      // 立即启用分享菜单（确保在游戏加载完成前就可用）
      this.enableShareMenu()
      
      // 根据游戏进度决定初始界面
      this.setInitialState()
      
      if (DEBUG_MODE) logger.info('游戏初始化完成！')
    } catch (error) {
      logger.error('游戏初始化失败:', error)
      this.showError('游戏初始化失败，请重试')
    }
  }














  /**
   * 转发给好友
   * @param {string} powerUpType - 道具类型
   */
  shareToFriend(powerUpType) {
    if (!wx.shareAppMessage) {
      this.showMessage('当前版本不支持转发功能', 2000)
      this.isInteractionEnabled = true
      return
    }

    logger.info(`🔄 开始转发分享，道具类型: ${powerUpType}`)

    // 创建分享内容
    const shareData = {
      title: '鸭了个鸭呀 - 超好玩的消除游戏！',
      desc: `我已经玩到第${this.currentLevel}关了，快来一起挑战吧！`,
      // 小游戏不需要path参数，微信会自动处理
      success: () => {
        logger.info('✅ 微信API报告转发成功')
        this.onShareSuccess(powerUpType, '转发')
      },
      fail: (err) => {
        logger.error('❌ 微信API报告转发失败:', err)
        this.showMessage('转发失败，请重试', 2000)
        this.isInteractionEnabled = true
      }
    }

    // 如果有自定义图片，可以添加imageUrl
    // shareData.imageUrl = 'path/to/share-image.jpg'

    try {
      wx.shareAppMessage(shareData)
      
      // 微信小游戏的success回调可能不可靠，使用延时处理
      // 假设用户在3秒内没有取消就认为分享成功
      logger.info('⏰ 设置分享超时检测')
      setTimeout(() => {
        // 检查交互是否仍被禁用（说明success/fail都没被调用）
        if (!this.isInteractionEnabled) {
          logger.info('⚠️ 微信分享回调未触发，假设分享成功')
          this.onShareSuccess(powerUpType, '转发')
        }
      }, 3000)
      
    } catch (error) {
      logger.error('❌ 调用微信分享API失败:', error)
      this.showMessage('分享功能不可用', 2000)
      this.isInteractionEnabled = true
    }
  }


  /**
   * 转发给好友（不给奖励，仅调用API）
   * @param {string} powerUpType - 道具类型
   */
  shareToFriendWithoutReward(powerUpType) {
    if (!wx.shareAppMessage) {
      logger.warn('微信分享API不可用')
      return
    }

    logger.info(`📤 调用微信分享API，道具类型: ${powerUpType}`)

    // 创建分享内容
    const shareData = {
      title: '鸭了个鸭呀 - 超好玩的消除游戏！',
      desc: `我已经玩到第${this.currentLevel}关了，快来一起挑战吧！`,
      success: () => {
        logger.info('✅ 微信API确认分享成功')
      },
      fail: (err) => {
        logger.error('❌ 微信API报告分享失败:', err)
      }
    }

    try {
      wx.shareAppMessage(shareData)
    } catch (error) {
      logger.error('❌ 调用微信分享API异常:', error)
    }
  }





  /**
   * 分享成功处理
   * @param {string} powerUpType - 道具类型
   * @param {string} shareType - 分享方式
   */
  onShareSuccess(powerUpType, shareType) {
    // 防止重复调用
    if (this.isInteractionEnabled) {
      logger.warn(`⚠️ 分享成功回调重复调用，忽略: ${shareType}`)
      return
    }
    
    logger.info(`🎉 分享成功: ${shareType}, 道具类型: ${powerUpType}`)
    
    // 分享成功后增加道具次数
    const oldCount = this.powerUps[powerUpType]
    this.powerUps[powerUpType]++
    const newCount = this.powerUps[powerUpType]
    
    logger.info(`📈 道具次数更新: ${powerUpType} ${oldCount} -> ${newCount}`)
    
    // 更新道具按钮显示
    this.updatePowerUpButtons()
    
    // 显示成功消息
    const powerUpName = powerUpType === 'undo' ? '撤回' : '洗牌'
    this.showMessage(`${shareType}成功！${powerUpName}次数+1`, 2000)
    
    // 恢复游戏交互
    this.isInteractionEnabled = true
    
    // 可选：播放成功音效
    if (this.audioManager) {
      this.audioManager.playSound('powerup')
    }
  }

  /**
   * 显示设置面板
   */
  showSettingsPanel() {
    if (this.settingsPanel) {
      this.settingsPanel.show()
    }
  }

  /**
   * 切换音效开关
   * @param {boolean} enabled - 是否启用音效
   */
  toggleSound(enabled) {
    this.audioManager.setSoundEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`音效${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 切换背景音乐开关
   * @param {boolean} enabled - 是否启用背景音乐
   */
  toggleMusic(enabled) {
    this.audioManager.setMusicEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`背景音乐${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 返回关卡选择
   */
  backToLevelSelect() {
    logger.info('返回关卡选择页面')
    this.setState('levelSelect')
  }

  /**
   * 处理意见反馈输入（小程序环境）
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 对话框点击回调（用于开发者模式触发）
   */
  handleFeedbackInput(currentText, callback, clickCallback) {
    // 创建自定义反馈对话框，支持点击计数
    this.showCustomFeedbackDialog(currentText, callback, clickCallback)
  }

  /**
   * 显示自定义反馈对话框
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 点击计数回调
   */
  showCustomFeedbackDialog(currentText, callback) {
    // 如果是微信小程序环境，使用prompt输入
    if (typeof wx !== 'undefined' && wx.showModal) {
      wx.showModal({
        title: '意见反馈',
        editable: true,
        placeholderText: currentText || '请输入您的意见和建议...',
        success: (res) => {
          if (res.confirm) {
            callback(res.content || currentText || '')
          } else {
            callback(currentText || '')
          }
        }
      })
    } else {
      // Web环境，使用prompt作为fallback
      const input = prompt('请输入您的意见和建议:', currentText || '')
      if (input !== null) {
        callback(input)
      } else {
        callback(currentText || '')
      }
    }
  }

  /**
   * 重新开始当前关卡
   */
  restartCurrentLevel() {
    logger.info(`🔄 重新开始关卡 ${this.currentLevel}`)
    
    // 重新开始当前关卡，保持关卡编号不变
    this.startLevel(this.currentLevel)
    
    // 显示提示信息
    this.showMessage(`重新开始第${this.currentLevel}关`, 2000)
  }

  /**
   * 更新文件信息面板内容
   */
  updateFileInfoPanel() {
    if (!this.fileInfoPanel) return

    const content = [
      `游戏版本: v1.0.0`,
      `当前关卡: ${this.currentLevel}`,
      `移动次数: ${this.moves}`,
      `游戏状态: ${this.gameState}`,
      `道具剩余: 撤回${this.powerUps.undo} 洗牌${this.powerUps.shuffle}`,
      `屏幕尺寸: ${this.canvasSize.screenWidth}x${this.canvasSize.screenHeight}`,
      `像素比: ${this.canvasSize.pixelRatio}`,
      `调试模式: ${this.debugMode ? '开启' : '关闭'}`
    ]

    this.fileInfoPanel.setContent(content)
  }

  /**
   * 处理方块点击
   * @param {Block} block - 被点击的方块
   */
  handleBlockClick(block) {
    logger.info(`点击了类型为 ${block.type} 的方块`);
    
    this.audioManager.playSound('click');
    
    const moveRecord = {
      block: block,
      originalX: block.x,
      originalY: block.y,
      originalLayer: block.layer,
      originalWidth: block.initialWidth,
      originalHeight: block.initialHeight,
      timestamp: Date.now()
    };
    
    block.playClickAnimation(this.animationManager);
    this.moves++;
    
    // 将方块添加到槽位
    this.slot.addBlock(block);

    // 从棋盘上移除方块
    this.board.removeBlock(block);
    this.moveHistory.push(moveRecord);
    if (this.moveHistory.length > 10) {
      this.moveHistory.shift();
    }
    this.updatePowerUpButtons();

    // 检查游戏失败条件
    if (this.slot.getBlockCount() > this.slot.maxBlocks) {
      // 槽位溢出，立即失败
      logger.info('槽位溢出（超过最大容量），游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else if (this.slot.isFull()) {
      // 槽位已满，等待动画完成再判定
      logger.info('槽位已满，等待动画完成后再判定游戏结束');
      this.waitForAnimationsAndCheckFailure(block);
    } else {
      // 其他情况，等待可能的消除动画完成后再检查游戏状态
      this.delayedCheckGameState();
    }
  }

  /**
   * 等待方块移动和消除动画完成后检查槽位状态
   * @param {Block} block - 最近放入槽位的方块
   */
  waitForAnimationsAndCheckFailure(block) {
    logger.info('等待方块移动和消除动画完成后检查槽位状态');
    const checkFn = () => {
      if (block.isMoving || this.slot.isEliminationInProgress()) {
        requestAnimationFrame(checkFn);
      } else {
        // 动画完成后先检查消除结果
        if (this.slot.isFull()) {
          logger.info('动画完成后槽位仍满，游戏失败');
          this.endTime = Date.now();
          this.audioManager.playSound('lose');
          this.setState('lose');
        } else {
          logger.info('动画完成后槽位未满，继续游戏');
          this.delayedCheckGameState();
        }
      }
    };
    checkFn();
  }

  /**
   * 方块消除回调
   * @param {number} type - 消除的方块类型
   * @param {number} count - 消除的数量
   */
  onBlockEliminated(type, count) {
    // 播放消除音效
    this.audioManager.playSound('eliminate')
    
    // 增加分数
    this.score += count * 100
    
    logger.info(`消除了 ${count} 个类型 ${type} 的方块，得分: ${count * 100}`)
  }

  /**
   * 消除动画完成回调
   * 在消除动画结束后重新检查游戏状态
   */
  onEliminationComplete() {
    if (DEBUG_MODE) logger.info('🎬 消除动画完成，重新检查游戏状态')

    // 在消除动画完成后，重新检查游戏状态
    if (this.slot.isFull()) {
      // 如果槽位仍然是满的，说明无法消除，游戏失败
      logger.info('消除动画完成后槽位仍满，游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else {
      // 检查是否胜利或其他游戏状态
      if (DEBUG_MODE) logger.info('🎯 消除动画完成，现在检查游戏胜利状态');
      this.checkGameState();
    }
  }

  /**
   * 延迟检查游戏状态（等待可能的消除动画）
   */
  delayedCheckGameState() {
    if (DEBUG_MODE) logger.info('⏰ 延迟检查游戏状态开始');

    // 如果槽位正在进行消除动画，不立即检查游戏状态
    if (this.slot.isEliminationInProgress()) {
      if (DEBUG_MODE) logger.info('🎬 槽位正在消除动画中，等待动画完成后再检查游戏状态');
      return;
    }

    // 给一个短暂的延迟，让消除动画有机会开始
    setTimeout(() => {
      if (this.slot.isEliminationInProgress()) {
        if (DEBUG_MODE) logger.info('🎬 检测到消除动画已开始，等待动画完成');
        return;
      }
      // 如果没有消除动画，立即检查游戏状态
      if (DEBUG_MODE) logger.info('✅ 没有消除动画，立即检查游戏状态');
      this.checkGameState();
    }, 50); // 50ms的短暂延迟
  }

  /**
   * 检查游戏状态
   */
  checkGameState() {
    if (this.board.isWin()) {
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('关卡完成！');
      this.saveLevelCompletion();
      this.audioManager.playSound('win');
      this.setState('win');
    } else if (!this.board.hasClickableBlocks() && !this.slot.isFull()) {
      // 之前这里是 this.slot.isFull() && !this.board.hasClickableBlocks()
      // 现在改为：如果板上没有可点击的了，并且槽还没满（如果满了会在handleBlockClick中处理），这也是一种死局
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('游戏失败（无可用方块且槽未满）！');
      this.audioManager.playSound('lose');
      this.setState('lose');
    }
    // 注意：如果 this.slot.isFull()，已经在 handleBlockClick 中处理了失败
    // 所以这里不再需要检查 this.slot.isFull() 的情况。
    // 保留一个可能性：如果棋盘清空，但槽是满的，board.isWin()可能为true，slot.isFull()也为true。
    // isWin() 应该优先于槽满失败。
    // 当前的 isWin() 是 this.blocks.filter(block => block.isVisible).length === 0 (棋盘上的方块)
    // 这是对的，胜利条件是清空棋盘。
  }

  /**
   * 保存关卡完成记录
   */
  saveLevelCompletion() {
    try {
      const playTime = Math.floor((this.endTime - this.startTime) / 1000) // 转换为秒
      const record = {
        score: this.score,
        moves: this.moves,
        playTime: playTime,
        timestamp: Date.now()
      }
      
      if (DEBUG_MODE) logger.info(`📱 保存关卡 ${this.currentLevel} 完成记录:`, record)
      
      // 使用存档管理器保存关卡完成记录（本地）
      this.saveManager.levelCompleted(this.currentLevel, record)
      
      // 检查是否为开发者模式
      if (this.saveManager.isDeveloperMode()) {
        logger.info('🛠️ 开发者模式：跳过服务器记录同步')
        return
      }
      
      // 如果用户已登录服务器，同时保存到服务器
      if (this.userManager && this.userManager.isConnectedToServer()) {
        logger.info(`🌐 保存关卡${this.currentLevel}记录到服务器...`)
        this.userManager.saveLevelProgress(
          this.currentLevel,
          this.score,
          this.moves,
          playTime
        ).then(result => {
          if (result.success) {
            logger.info(`✅ 关卡${this.currentLevel}记录已同步到服务器`)
            // 如果是最佳记录，显示提示
            if (result.data && result.data.isBestRecord) {
              this.showMessage(`🎉 刷新了关卡${this.currentLevel}的最佳记录！`, 3000)
            }
          } else {
            logger.warn(`⚠️ 关卡${this.currentLevel}记录同步到服务器失败:`, result.error)
          }
        }).catch(error => {
          logger.error(`❌ 关卡${this.currentLevel}记录同步到服务器异常:`, error)
        })
      } else {
        logger.info('🔒 未连接到服务器，仅保存本地记录')
      }
    } catch (error) {
      logger.error('❌ 保存关卡记录失败:', error)
    }
  }

  /**
   * 开始指定关卡
   * @param {number} level - 关卡编号
   */
  startLevel(level) {
    if (DEBUG_MODE) logger.info(`开始关卡 ${level}`)
    
    this.currentLevel = level
    
    // 更新全局变量用于调试（Block.js会用到）
    if (typeof window !== 'undefined') {
      window.currentLevel = level
    }
    
    this.score = 0
    this.moves = 0
    this.startTime = Date.now()
    this.endTime = 0 // 重置结束时间
    this.moveHistory = [] // 清空移动历史
    
    // 重置道具次数（每关重置）
    this.powerUps = {
      undo: 3,
      shuffle: 2
    }
    
    // 清空槽位
    this.slot.clear()
    
    // 初始化游戏板
    this.board.initLevel(level)
    
    // 更新道具按钮
    this.updatePowerUpButtons()
    
    // 播放背景音乐
    this.audioManager.playBackgroundMusic()
    
    // 设置游戏状态
    this.setState('playing')
  }

  /**
   * 设置游戏状态
   * @param {string} state - 新状态
   */
  setState(state) {
    if (DEBUG_MODE) logger.info(`游戏状态变更: ${this.gameState} -> ${state}`)
    this.gameState = state
    
    // 根据状态进行相应处理
    switch (state) {
      case 'loading':
        this.isInteractionEnabled = false
        break
      case 'menu':
        this.isInteractionEnabled = true
        break
      case 'levelSelect':
        this.isInteractionEnabled = true
        // 进入关卡选择时，尝试加载服务器进度（开发者模式下跳过）
        if (!this.saveManager.isDeveloperMode()) {
          this.serverProgressManager.loadServerProgress()
        } else {
          logger.info('🛠️ 开发者模式：跳过服务器进度加载')
        }
        break
      case 'playing':
        this.isInteractionEnabled = true
        break
      case 'paused':
        this.isInteractionEnabled = false
        break
      case 'win':
        this.isInteractionEnabled = true
        // 显示美化的胜利页面
        if (this.winScreen) {
          // 计算用时，确保不为负数
          let playTime = 0
          if (this.endTime > 0 && this.startTime > 0 && this.endTime >= this.startTime) {
            playTime = Math.floor((this.endTime - this.startTime) / 1000)
          }

          this.winScreen.show({
            time: playTime,
            score: this.score,
            moves: this.moves
          })
        }
        break
      case 'lose':
        this.isInteractionEnabled = true
        break
    }
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    logger.error(message)
    // 这里可以显示错误对话框
  }

  /**
   * 开始游戏循环
   */
  startGameLoop() {
    let lastTime = 0
    
    const gameLoop = (timestamp) => {
      const deltaTime = timestamp - lastTime
      lastTime = timestamp
      
      // 更新游戏状态
      this.update(deltaTime)
      
      // 渲染游戏画面
      this.render()
      
      // 请求下一帧
      requestAnimationFrame(gameLoop)
    }
    
    requestAnimationFrame(gameLoop)
    if (DEBUG_MODE) logger.info('游戏循环已启动')
  }

  /**
   * 更新游戏状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    // 更新动画管理器
    this.animationManager.update(Date.now())

    // 更新文件信息面板
    if (this.fileInfoPanel) {
      this.fileInfoPanel.update(deltaTime)
    }

    // 更新设置UI
    if (this.settingsButton) {
      this.settingsButton.update(deltaTime)
    }
    if (this.settingsPanel) {
      this.settingsPanel.update(deltaTime)
    }

    // 更新分享对话框
    if (this.shareDialog) {
      this.shareDialog.update(deltaTime)
    }

    // 更新胜利页面
    if (this.winScreen && this.gameState === 'win') {
      this.winScreen.update(deltaTime)
    }

    if (this.gameState === 'playing') {
      // 更新游戏对象
      if (this.board) {
        this.board.update(deltaTime)
      }
      
      if (this.slot) {
        this.slot.update(deltaTime)
      }
      
      // 更新道具按钮
      this.powerUpButtons.forEach(button => {
        button.update(deltaTime)
      })

      // 定期更新文件信息面板内容
      if (this.fileInfoPanel && Date.now() % 1000 < 50) { // 大约每秒更新一次
        this.updateFileInfoPanel()
      }
    }
  }

  /**
   * 渲染游戏画面
   */
  render() {
    // 委托给渲染管理器
    this.renderManager.render()
  }











































  /**
   * 启用分享菜单
   */
  enableShareMenu() {
    if (typeof wx !== 'undefined') {
      try {
        // 显式启用分享菜单
        if (wx.showShareMenu) {
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          })
          logger.info('🎯 分享菜单已启用：转发好友、分享朋友圈')
        }

        // 启用分享到朋友圈（如果支持）
        if (wx.showShareTimelineMenu) {
          wx.showShareTimelineMenu({
            success: () => {
              logger.info('✅ 朋友圈分享菜单已启用')
            },
            fail: (err) => {
              logger.warn('⚠️ 朋友圈分享菜单启用失败:', err)
            }
          })
        }

        // 启用收藏功能（如果支持）
        if (wx.showFavoriteGuide) {
          wx.showFavoriteGuide({
            type: 'bar',
            content: '一键收藏，快速进入小游戏',
            success: () => {
              logger.info('✅ 收藏引导已显示')
            }
          })
        }

        // 检查分享菜单API可用性
        logger.info('🔍 分享API检查:')
        logger.info('- wx.showShareMenu:', typeof wx.showShareMenu)
        logger.info('- wx.onShareAppMessage:', typeof wx.onShareAppMessage)
        logger.info('- wx.shareAppMessage:', typeof wx.shareAppMessage)
        logger.info('- wx.onShareTimeline:', typeof wx.onShareTimeline)
        logger.info('- wx.shareTimeline:', typeof wx.shareTimeline)

      } catch (error) {
        logger.error('❌ 启用分享菜单失败:', error)
      }
    }
  }

  /**
   * 设置微信小游戏生命周期监听
   */
  setupWechatLifeCycle() {
    // 游戏进入后台
    wx.onHide(() => {
      logger.info('游戏进入后台')
      if (this.gameState === 'playing') {
        this.setState('paused')
      }
    })

    // 游戏回到前台
    wx.onShow(() => {
      logger.info('游戏回到前台')
      if (this.gameState === 'paused') {
        this.setState('playing')
      }
    })

    // 显式启用分享菜单（重复调用确保生效）
    if (wx.showShareMenu) {
      // 第一次调用
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline'],
        success: () => {
          logger.info('✅ 第一次分享菜单启用成功')
        },
        fail: (err) => {
          logger.error('❌ 第一次分享菜单启用失败:', err)
        }
      })
      
      // 延时再次调用确保生效
      setTimeout(() => {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline'],
          success: () => {
            logger.info('✅ 第二次分享菜单启用成功')
          },
          fail: (err) => {
            logger.warn('⚠️ 第二次分享菜单启用失败:', err)
          }
        })
      }, 1000)
    }

    // 设置被动分享（用户点击右上角菜单时）
    if (wx.onShareAppMessage) {
      wx.onShareAppMessage(() => {
        logger.info('🚀 用户通过右上角菜单转发给好友')
        return this.getShareData()
      })
    }

    // 设置朋友圈分享（如果支持）
    if (wx.onShareTimeline) {
      wx.onShareTimeline(() => {
        logger.info('📱 用户通过右上角菜单分享到朋友圈')
        return this.getTimelineShareData()
      })
    }

    // 监听复制链接（如果支持）
    if (wx.onCopyUrl) {
      wx.onCopyUrl(() => {
        logger.info('📋 用户通过右上角菜单复制链接')
        return {
          title: `鸭了个鸭呀 - 第${this.currentLevel}关`,
          path: `/?from=copy&level=${this.currentLevel}&score=${this.score}`,
          query: `level=${this.currentLevel}&score=${this.score}`
        }
      })
    }
  }

  /**
   * 获取分享数据
   * @returns {Object} 分享数据
   */
  getShareData() {
    // 根据游戏状态生成不同的分享文案
    let title, desc
    
    if (this.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀中通过了第${this.currentLevel}关！`
      desc = `得分${this.score}分，用了${this.moves}步！你能超越我吗？`
    } else if (this.gameState === 'lose') {
      title = `💪 我在鸭了个鸭呀中挑战第${this.currentLevel}关！`
      desc = `虽然失败了，但是很好玩！快来试试能不能比我厉害！`
    } else {
      title = `🦆 我正在玩鸭了个鸭呀，已经到第${this.currentLevel}关了！`
      desc = `超好玩的消除游戏，快来一起挑战吧！`
    }
    
    return {
      title: title,
      desc: desc,
      query: `from=friend&level=${this.currentLevel}&score=${this.score}`,
      // imageUrl: 'path/to/share-image.jpg', // 可以添加自定义分享图片
    }
  }

  /**
   * 获取朋友圈分享数据
   * @returns {Object} 朋友圈分享数据
   */
  getTimelineShareData() {
    let title
    
    if (this.gameState === 'win') {
      title = `🎉 我在鸭了个鸭呀游戏中通过了第${this.currentLevel}关，得分${this.score}分！你敢来挑战吗？`
    } else if (this.gameState === 'lose') {
      title = `💪 鸭了个鸭呀第${this.currentLevel}关太难了！有人能帮我通关吗？`
    } else {
      title = `🦆 我在鸭了个鸭呀游戏中玩到第${this.currentLevel}关了！来挑战看看你能玩到第几关？`
    }
    
    return {
      title: title,
      query: `from=timeline&level=${this.currentLevel}&score=${this.score}`,
      // imageUrl: 'path/to/timeline-image.jpg', // 可以添加自定义分享图片
    }
  }



  /**
   * 根据游戏进度决定初始界面
   */
  setInitialState() {
    if (this.isFirstPlay) {
      // 首次游戏，显示菜单
      this.setState('menu')
    } else {
      // 返回玩家，直接进入关卡选择
      this.setState('levelSelect')
    }
  }

  /**
   * 使用道具
   * @param {string} type - 道具类型 ('undo', 'shuffle')
   */
  usePowerUp(type) {
    if (this.gameState !== 'playing') return
    
    // 如果道具次数已用完，显示分享对话框
    if (this.powerUps[type] <= 0) {
      logger.info(`🎯 道具${type}次数已用完，显示分享对话框`)
      this.shareManager.showShareDialog(type)
      return
    }

    logger.info(`🎯 尝试使用道具: ${type}，剩余次数: ${this.powerUps[type]}`)

    // 播放道具音效
    this.audioManager.playSound('powerup')

    let success = false  // 道具使用是否成功
    switch (type) {
      case 'undo':
        success = this.usePowerUpUndo()
        break
      case 'shuffle':
        success = this.usePowerUpShuffle()
        break
    }

    // 只有在道具使用成功时才减少次数
    if (success) {
      this.powerUps[type]--
      logger.info(`✅ 道具${type}使用成功，剩余次数: ${this.powerUps[type]}`)
      this.updatePowerUpButtons()
    } else {
      logger.info(`❌ 道具${type}使用失败，次数未扣除，剩余次数: ${this.powerUps[type]}`)
    }
  }



  /**
   * 使用撤回道具 - 撤回最后一次移动
   * @returns {boolean} 是否成功执行撤回操作
   */
  usePowerUpUndo() {
    if (this.moveHistory.length === 0) {
      logger.info('🚫 没有可撤回的移动，无法使用撤回道具')
      return false  // 返回失败状态
    }

    const lastMove = this.moveHistory.pop()
    logger.info('↩️ 撤回最后一次移动')

    // 从槽位移除方块
    this.slot.removeBlock(lastMove.block)
    
    // 重置方块状态，清除动画残留
    const block = lastMove.block
    logger.info(`撤回前尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)
    block.reset()

    // 恢复方块的原始大小和位置
    lastMove.block.setPosition(lastMove.originalX, lastMove.originalY)
    lastMove.block.layer = lastMove.originalLayer
    block.width = lastMove.originalWidth   // 恢复原始宽度
    block.height = lastMove.originalHeight // 恢复原始高度
    logger.info(`撤回后尺寸: ${block.width}x${block.height}, scale: ${block.scale}, alpha: ${block.alpha}`)

    // 重新添加到游戏板
    this.board.blocks.push(lastMove.block)
    this.board.updateLayers()
    this.board.updateClickableStates()

    // 减少移动次数
    this.moves--
    
    logger.info(`✅ 撤回完成: 方块大小恢复为 ${lastMove.originalWidth}x${lastMove.originalHeight}`)
    return true  // 返回成功状态
  }

  /**
   * 使用洗牌道具 - 重新随机排列方块位置
   * @returns {boolean} 是否成功执行洗牌操作
   */
  usePowerUpShuffle() {
    logger.info('🔀 执行洗牌操作')
    
    // 调用游戏板的洗牌方法
    this.board.shuffle()
    
    logger.info('✅ 洗牌完成')
    return true  // 返回成功状态
  }

  /**
   * 更新道具按钮显示
   */
  updatePowerUpButtons() {
    this.powerUpButtons.forEach((button, index) => {
      const types = ['undo', 'shuffle']
      const type = types[index]
      const count = this.powerUps[type]
      
      // 更新按钮文字
      const labels = ['撤回', '洗牌']
      button.setText(`${labels[index]}(${count})`)
      
      // 按钮始终可以点击，次数为0时点击会弹出分享对话框
      button.setEnabled(true)
      
      // 如果次数为0，可以改变按钮的视觉样式来提示用户
      if (count <= 0) {
        button.setTheme(index === 0 ? 'orange' : 'green') // 改变颜色提示可以分享获得
      } else {
        button.setTheme(index === 0 ? 'blue' : 'orange') // 恢复原来的颜色
      }
    })
  }

  /**
   * 显示临时消息
   * @param {string} message - 要显示的消息
   * @param {number} duration - 显示时长（毫秒）
   */
  showMessage(message, duration = 2000) {
    this.temporaryMessage = {
      text: message,
      startTime: Date.now(),
      duration: duration
    }
    
    // 自动清除消息
    setTimeout(() => {
      if (this.temporaryMessage && this.temporaryMessage.text === message) {
        this.temporaryMessage = null
      }
    }, duration)
  }
}

// CommonJS导出
module.exports = Main