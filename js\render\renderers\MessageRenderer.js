const UtilityRenderer = require('./UtilityRenderer.js')

/**
 * 消息渲染器
 * 负责临时消息的渲染，包括淡入淡出效果和美化样式
 */
class MessageRenderer {
  /**
   * 构造函数
   * @param {Object} main - 主游戏实例
   */
  constructor(main) {
    this.main = main
  }

  /**
   * 渲染临时消息
   */
  renderTemporaryMessage() {
    const temporaryMessage = this.main.getTemporaryMessage()
    if (!temporaryMessage) return

    const now = Date.now()
    const elapsed = now - temporaryMessage.startTime

    // 检查消息是否过期
    if (elapsed > temporaryMessage.duration) {
      return
    }
    
    const ctx = this.main.ctx
    const centerX = this.main.canvasSize.screenWidth / 2
    const centerY = this.main.canvasSize.screenHeight / 3
    
    // 计算透明度（淡入淡出效果）
    const fadeTime = 300 // 300ms淡入淡出时间
    let alpha = 1
    
    if (elapsed < fadeTime) {
      // 淡入
      alpha = elapsed / fadeTime
    } else if (elapsed > temporaryMessage.duration - fadeTime) {
      // 淡出
      alpha = (temporaryMessage.duration - elapsed) / fadeTime
    }

    ctx.save()
    ctx.globalAlpha = alpha

    // 绘制消息背景
    const padding = 20
    const textMetrics = ctx.measureText(temporaryMessage.text)
    const textWidth = textMetrics.width
    const textHeight = 24
    const bgWidth = textWidth + padding * 2
    const bgHeight = textHeight + padding
    
    // 背景渐变
    const gradient = ctx.createLinearGradient(
      centerX - bgWidth / 2, centerY - bgHeight / 2,
      centerX + bgWidth / 2, centerY + bgHeight / 2
    )
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)')
    gradient.addColorStop(1, 'rgba(50, 50, 50, 0.8)')
    
    ctx.fillStyle = gradient
    UtilityRenderer.drawRoundedRect(ctx, centerX - bgWidth / 2, centerY - bgHeight / 2, bgWidth, bgHeight, 12)
    ctx.fill()
    
    // 绘制边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1
    UtilityRenderer.drawRoundedRect(ctx, centerX - bgWidth / 2, centerY - bgHeight / 2, bgWidth, bgHeight, 12)
    ctx.stroke()
    
    // 绘制消息文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(temporaryMessage.text, centerX, centerY)
    
    ctx.restore()
  }
}

// CommonJS导出
module.exports = MessageRenderer
