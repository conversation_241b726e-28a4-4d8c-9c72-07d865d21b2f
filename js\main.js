const Utils = require('./utils/Utils.js')
const ResourceLoader = require('./utils/ResourceLoader.js')
const EventManager = require('./utils/EventManager.js')
const ImageGenerator = require('./utils/ImageGenerator.js')
const CartoonImageLoader = require('./utils/CartoonImageLoader.js')
const AnimationManager = require('./utils/AnimationManager.js')
const ErrorHandler = require('./utils/ErrorHandler.js')
const AudioManager = require('./utils/AudioManager.js')
const GameSaveManager = require('./utils/GameSaveManager.js')
const UserManager = require('./utils/UserManager.js')
const NetworkManager = require('./utils/NetworkManager.js')
const Board = require('./game/Board.js')
const Slot = require('./game/Slot.js')
const Button = require('./ui/Button.js')
const ParticleSystem = require('./effects/ParticleSystem.js')
const WinScreen = require('./ui/WinScreen.js')
const CollapsiblePanel = require('./ui/CollapsiblePanel.js')
const SettingsButton = require('./ui/SettingsButton.js')
const SettingsPanel = require('./ui/SettingsPanel.js')
const ShareDialog = require('./ui/ShareDialog.js')
const RenderManager = require('./render/RenderManager.js')
const GameInitializer = require('./core/GameInitializer.js')
const ShareManager = require('./core/ShareManager.js')
const TouchEventHandler = require('./core/TouchEventHandler.js')
const ServerProgressManager = require('./core/ServerProgressManager.js')
const UIRenderer = require('./core/UIRenderer.js')
const logger = require('./utils/AsyncLogger')

// 新增的管理器模块
const GameStateManager = require('./core/GameStateManager.js')
const PowerUpManager = require('./core/PowerUpManager.js')
const MessageManager = require('./core/MessageManager.js')
const WechatLifeCycleManager = require('./core/WechatLifeCycleManager.js')
const GameLoopManager = require('./core/GameLoopManager.js')
const GameProgressManager = require('./core/GameProgressManager.js')


// 全局DEBUG配置 - 设为false关闭所有调试日志
const DEBUG_MODE = false

// 添加调试日志 - 现在受DEBUG_MODE控制
if (DEBUG_MODE) {
  logger.info('📦 主模块依赖加载情况:')
  logger.info('- Utils:', typeof Utils)
  logger.info('- ResourceLoader:', typeof ResourceLoader)
  logger.info('- EventManager:', typeof EventManager)
  logger.info('- ImageGenerator:', typeof ImageGenerator)
  logger.info('- CartoonImageLoader:', typeof CartoonImageLoader)
  logger.info('- AnimationManager:', typeof AnimationManager)
  logger.info('- ErrorHandler:', typeof ErrorHandler)
  logger.info('- AudioManager:', typeof AudioManager)
  logger.info('- GameSaveManager:', typeof GameSaveManager)
  logger.info('- UserManager:', typeof UserManager)
  logger.info('- NetworkManager:', typeof NetworkManager)
  logger.info('- Board:', typeof Board)
  logger.info('- Slot:', typeof Slot)
  logger.info('- Button:', typeof Button)
  logger.info('- CollapsiblePanel:', typeof CollapsiblePanel)
  logger.info('- SettingsButton:', typeof SettingsButton)
  logger.info('- SettingsPanel:', typeof SettingsPanel)
}

/**
 * 游戏主控制器
 * 管理游戏的整体流程、Canvas渲染和用户交互
 */
class Main {
  constructor() {
    this.canvas = null
    this.ctx = null
    this.resources = new ResourceLoader()
    this.eventManager = new EventManager()
    this.animationManager = new AnimationManager()
    this.errorHandler = new ErrorHandler()
    this.audioManager = new AudioManager()
    this.userManager = new UserManager()
    this.saveManager = new GameSaveManager()
    
    // 游戏状态
    this.gameState = 'loading' // loading, menu, playing, paused, win, lose
    this.currentLevel = 1
    this.score = 0
    this.moves = 0
    this.startTime = 0
    this.endTime = 0
    
    // 游戏对象
    this.board = null
    this.slot = null
    this.powerUpButtons = [] // 道具按钮
    this.fileInfoPanel = null // 文件信息面板
    this.settingsButton = null // 设置按钮
    this.settingsPanel = null // 设置面板
    this.shareDialog = null // 分享对话框
    
    // 道具使用次数
    this.powerUps = {
      undo: 3,      // 撤回次数 
      shuffle: 2    // 洗牌次数
    }
    this.moveHistory = [] // 移动历史，用于撤回
    
    // 交互状态
    this.isInteractionEnabled = true
    this.lastTouchTime = 0
    this.touchDebounceTime = 100 // 防止重复点击
    
    // 调试模式 - 关闭调试模式以提升性能
    this.debugMode = false
    
    // 调试手势检测
    this.debugGestureClicks = []
    this.debugGestureTimeout = null
    
    // 屏幕适配 - 在setupCanvas中初始化
    this.canvasSize = null
    
    // 服务器进度数据
    this.serverProgress = null
    this.isLoadingServerProgress = false

    // 初始化渲染管理器
    this.renderManager = new RenderManager(this)

    // 初始化原有的模块化管理器
    this.gameInitializer = new GameInitializer(this)
    this.shareManager = new ShareManager(this)
    this.touchEventHandler = new TouchEventHandler(this)
    this.serverProgressManager = new ServerProgressManager(this)
    this.uiRenderer = new UIRenderer(this)

    // 初始化新的管理器模块
    this.gameStateManager = new GameStateManager(this)
    this.powerUpManager = new PowerUpManager(this)
    this.messageManager = new MessageManager(this)
    this.wechatLifeCycleManager = new WechatLifeCycleManager(this)
    this.gameLoopManager = new GameLoopManager(this)
    this.gameProgressManager = new GameProgressManager(this)

    this.init()
  }

  /**
   * 初始化游戏
   */
  async init() {
    if (DEBUG_MODE) logger.info('初始化游戏...')
    
    try {
      // 创建Canvas
      this.gameInitializer.setupCanvas()

      // 设置触摸事件
      this.touchEventHandler.setupTouchEvents()

      // 加载资源
      await this.gameInitializer.loadResources()

      // 初始化音频系统
      await this.audioManager.init()

      // 初始化用户系统
      await this.gameInitializer.initUserSystem()

      // 加载游戏进度
      this.gameInitializer.loadGameProgress()

      // 初始化游戏对象
      this.gameInitializer.initGameObjects()

      // 开始游戏循环
      this.gameLoopManager.startGameLoop()

      // 设置微信小游戏生命周期监听
      this.wechatLifeCycleManager.setupWechatLifeCycle()

      // 立即启用分享菜单（确保在游戏加载完成前就可用）
      this.wechatLifeCycleManager.enableShareMenu()

      // 根据游戏进度决定初始界面
      this.gameStateManager.setInitialState()
      
      if (DEBUG_MODE) logger.info('游戏初始化完成！')
    } catch (error) {
      logger.error('游戏初始化失败:', error)
      this.messageManager.showError('游戏初始化失败，请重试')
    }
  }

  /**
   * 分享成功处理
   * @param {string} powerUpType - 道具类型
   * @param {string} shareType - 分享方式
   */
  onShareSuccess(powerUpType, shareType) {
    // 防止重复调用
    if (this.isInteractionEnabled) {
      logger.warn(`⚠️ 分享成功回调重复调用，忽略: ${shareType}`)
      return
    }

    logger.info(`🎉 分享成功: ${shareType}, 道具类型: ${powerUpType}`)

    // 分享成功后增加道具次数
    const oldCount = this.powerUps[powerUpType]
    this.powerUps[powerUpType]++
    const newCount = this.powerUps[powerUpType]

    logger.info(`📈 道具次数更新: ${powerUpType} ${oldCount} -> ${newCount}`)

    // 更新道具按钮显示
    this.powerUpManager.updatePowerUpButtons()

    // 显示成功消息
    const powerUpName = powerUpType === 'undo' ? '撤回' : '洗牌'
    this.messageManager.showMessage(`${shareType}成功！${powerUpName}次数+1`, 2000)

    // 恢复游戏交互
    this.isInteractionEnabled = true

    // 可选：播放成功音效
    if (this.audioManager) {
      this.audioManager.playSound('powerup')
    }
  }

  /**
   * 显示设置面板
   */
  showSettingsPanel() {
    if (this.settingsPanel) {
      this.settingsPanel.show()
    }
  }

  /**
   * 切换音效开关
   * @param {boolean} enabled - 是否启用音效
   */
  toggleSound(enabled) {
    this.audioManager.setSoundEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`音效${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 切换背景音乐开关
   * @param {boolean} enabled - 是否启用背景音乐
   */
  toggleMusic(enabled) {
    this.audioManager.setMusicEnabled(enabled)
    this.audioManager.saveSettings()
    logger.info(`背景音乐${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 返回关卡选择
   */
  backToLevelSelect() {
    this.gameStateManager.backToLevelSelect()
  }

  /**
   * 处理意见反馈输入（小程序环境）
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 对话框点击回调（用于开发者模式触发）
   */
  handleFeedbackInput(currentText, callback, clickCallback) {
    this.messageManager.handleFeedbackInput(currentText, callback, clickCallback)
  }

  /**
   * 重新开始当前关卡
   */
  restartCurrentLevel() {
    this.gameStateManager.restartCurrentLevel()
  }

  /**
   * 更新文件信息面板内容
   */
  updateFileInfoPanel() {
    this.gameLoopManager.updateFileInfoPanel()
  }

  /**
   * 处理方块点击
   * @param {Block} block - 被点击的方块
   */
  handleBlockClick(block) {
    logger.info(`点击了类型为 ${block.type} 的方块`);
    
    this.audioManager.playSound('click');

    // 记录移动历史
    this.powerUpManager.recordMove(block);

    block.playClickAnimation(this.animationManager);
    this.moves++;

    // 将方块添加到槽位
    this.slot.addBlock(block);

    // 从棋盘上移除方块
    this.board.removeBlock(block);

    this.powerUpManager.updatePowerUpButtons();

    // 检查游戏失败条件
    if (this.slot.getBlockCount() > this.slot.maxBlocks) {
      // 槽位溢出，立即失败
      logger.info('槽位溢出（超过最大容量），游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else if (this.slot.isFull()) {
      // 槽位已满，等待动画完成再判定
      logger.info('槽位已满，等待动画完成后再判定游戏结束');
      this.waitForAnimationsAndCheckFailure(block);
    } else {
      // 其他情况，等待可能的消除动画完成后再检查游戏状态
      this.delayedCheckGameState();
    }
  }

  /**
   * 等待方块移动和消除动画完成后检查槽位状态
   * @param {Block} block - 最近放入槽位的方块
   */
  waitForAnimationsAndCheckFailure(block) {
    logger.info('等待方块移动和消除动画完成后检查槽位状态');
    const checkFn = () => {
      if (block.isMoving || this.slot.isEliminationInProgress()) {
        requestAnimationFrame(checkFn);
      } else {
        // 动画完成后先检查消除结果
        if (this.slot.isFull()) {
          logger.info('动画完成后槽位仍满，游戏失败');
          this.endTime = Date.now();
          this.audioManager.playSound('lose');
          this.setState('lose');
        } else {
          logger.info('动画完成后槽位未满，继续游戏');
          this.delayedCheckGameState();
        }
      }
    };
    checkFn();
  }

  /**
   * 方块消除回调
   * @param {number} type - 消除的方块类型
   * @param {number} count - 消除的数量
   */
  onBlockEliminated(type, count) {
    // 播放消除音效
    this.audioManager.playSound('eliminate')
    
    // 增加分数
    this.score += count * 100
    
    logger.info(`消除了 ${count} 个类型 ${type} 的方块，得分: ${count * 100}`)
  }

  /**
   * 消除动画完成回调
   * 在消除动画结束后重新检查游戏状态
   */
  onEliminationComplete() {
    if (DEBUG_MODE) logger.info('🎬 消除动画完成，重新检查游戏状态')

    // 在消除动画完成后，重新检查游戏状态
    if (this.slot.isFull()) {
      // 如果槽位仍然是满的，说明无法消除，游戏失败
      logger.info('消除动画完成后槽位仍满，游戏失败！');
      this.endTime = Date.now();
      this.audioManager.playSound('lose');
      this.setState('lose');
    } else {
      // 检查是否胜利或其他游戏状态
      if (DEBUG_MODE) logger.info('🎯 消除动画完成，现在检查游戏胜利状态');
      this.checkGameState();
    }
  }

  /**
   * 延迟检查游戏状态（等待可能的消除动画）
   */
  delayedCheckGameState() {
    if (DEBUG_MODE) logger.info('⏰ 延迟检查游戏状态开始');

    // 如果槽位正在进行消除动画，不立即检查游戏状态
    if (this.slot.isEliminationInProgress()) {
      if (DEBUG_MODE) logger.info('🎬 槽位正在消除动画中，等待动画完成后再检查游戏状态');
      return;
    }

    // 给一个短暂的延迟，让消除动画有机会开始
    setTimeout(() => {
      if (this.slot.isEliminationInProgress()) {
        if (DEBUG_MODE) logger.info('🎬 检测到消除动画已开始，等待动画完成');
        return;
      }
      // 如果没有消除动画，立即检查游戏状态
      if (DEBUG_MODE) logger.info('✅ 没有消除动画，立即检查游戏状态');
      this.checkGameState();
    }, 50); // 50ms的短暂延迟
  }

  /**
   * 检查游戏状态
   */
  checkGameState() {
    if (this.board.isWin()) {
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('关卡完成！');
      this.saveLevelCompletion();
      this.audioManager.playSound('win');
      this.setState('win');
    } else if (!this.board.hasClickableBlocks() && !this.slot.isFull()) {
      // 之前这里是 this.slot.isFull() && !this.board.hasClickableBlocks()
      // 现在改为：如果板上没有可点击的了，并且槽还没满（如果满了会在handleBlockClick中处理），这也是一种死局
      this.endTime = Date.now();
      if (DEBUG_MODE) logger.info('游戏失败（无可用方块且槽未满）！');
      this.audioManager.playSound('lose');
      this.setState('lose');
    }
    // 注意：如果 this.slot.isFull()，已经在 handleBlockClick 中处理了失败
    // 所以这里不再需要检查 this.slot.isFull() 的情况。
    // 保留一个可能性：如果棋盘清空，但槽是满的，board.isWin()可能为true，slot.isFull()也为true。
    // isWin() 应该优先于槽满失败。
    // 当前的 isWin() 是 this.blocks.filter(block => block.isVisible).length === 0 (棋盘上的方块)
    // 这是对的，胜利条件是清空棋盘。
  }

  /**
   * 保存关卡完成记录
   */
  saveLevelCompletion() {
    this.gameProgressManager.saveLevelCompletion()
  }

  /**
   * 开始指定关卡
   * @param {number} level - 关卡编号
   */
  startLevel(level) {
    this.gameStateManager.startLevel(level)
  }

  /**
   * 设置游戏状态
   * @param {string} state - 新状态
   */
  setState(state) {
    this.gameStateManager.setState(state)
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    this.messageManager.showError(message)
  }

  /**
   * 开始游戏循环
   */
  startGameLoop() {
    this.gameLoopManager.startGameLoop()
  }

  /**
   * 更新游戏状态
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    this.gameLoopManager.update(deltaTime)
  }

  /**
   * 渲染游戏画面
   */
  render() {
    this.gameLoopManager.render()
  }

  /**
   * 获取分享数据（委托给微信生命周期管理器）
   * @returns {Object} 分享数据
   */
  getShareData() {
    return this.wechatLifeCycleManager.getShareData()
  }

  /**
   * 获取朋友圈分享数据（委托给微信生命周期管理器）
   * @returns {Object} 朋友圈分享数据
   */
  getTimelineShareData() {
    return this.wechatLifeCycleManager.getTimelineShareData()
  }





  /**
   * 使用道具
   * @param {string} type - 道具类型 ('undo', 'shuffle')
   */
  usePowerUp(type) {
    this.powerUpManager.usePowerUp(type)
  }

  /**
   * 更新道具按钮显示
   */
  updatePowerUpButtons() {
    this.powerUpManager.updatePowerUpButtons()
  }

  /**
   * 显示临时消息
   * @param {string} message - 要显示的消息
   * @param {number} duration - 显示时长（毫秒）
   */
  showMessage(message, duration = 2000) {
    this.messageManager.showMessage(message, duration)
  }

  /**
   * 获取临时消息（用于渲染）
   * @returns {Object|null} 当前显示的临时消息
   */
  getTemporaryMessage() {
    return this.messageManager.getTemporaryMessage()
  }
}

// CommonJS导出
module.exports = Main