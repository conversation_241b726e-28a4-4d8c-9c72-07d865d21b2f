const logger = require('../utils/AsyncLogger')

/**
 * 消息管理器
 * 负责管理游戏中的消息显示系统，包括临时消息、错误信息和反馈对话框
 */
class MessageManager {
  constructor(main) {
    this.main = main
    this.temporaryMessage = null
  }

  /**
   * 显示临时消息
   * @param {string} message - 要显示的消息
   * @param {number} duration - 显示时长（毫秒）
   */
  showMessage(message, duration = 2000) {
    this.temporaryMessage = {
      text: message,
      startTime: Date.now(),
      duration: duration
    }
    
    // 自动清除消息
    setTimeout(() => {
      if (this.temporaryMessage && this.temporaryMessage.text === message) {
        this.temporaryMessage = null
      }
    }, duration)
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    logger.error(message)
    // 这里可以显示错误对话框
    this.showMessage(`错误: ${message}`, 3000)
  }

  /**
   * 处理意见反馈输入（小程序环境）
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 对话框点击回调（用于开发者模式触发）
   */
  handleFeedbackInput(currentText, callback, clickCallback) {
    // 创建自定义反馈对话框，支持点击计数
    this.showCustomFeedbackDialog(currentText, callback, clickCallback)
  }

  /**
   * 显示自定义反馈对话框
   * @param {string} currentText - 当前文字
   * @param {function} callback - 输入完成回调
   * @param {function} clickCallback - 点击计数回调
   */
  showCustomFeedbackDialog(currentText, callback) {
    // 如果是微信小程序环境，使用prompt输入
    if (typeof wx !== 'undefined' && wx.showModal) {
      wx.showModal({
        title: '意见反馈',
        editable: true,
        placeholderText: currentText || '请输入您的意见和建议...',
        success: (res) => {
          if (res.confirm) {
            callback(res.content || currentText || '')
          } else {
            callback(currentText || '')
          }
        }
      })
    } else {
      // Web环境，使用prompt作为fallback
      const input = prompt('请输入您的意见和建议:', currentText || '')
      if (input !== null) {
        callback(input)
      } else {
        callback(currentText || '')
      }
    }
  }

  /**
   * 获取当前临时消息
   * @returns {Object|null} 当前显示的临时消息
   */
  getTemporaryMessage() {
    return this.temporaryMessage
  }

  /**
   * 清除临时消息
   */
  clearTemporaryMessage() {
    this.temporaryMessage = null
  }

  /**
   * 检查消息是否过期并自动清除
   */
  updateMessages() {
    if (this.temporaryMessage) {
      const elapsed = Date.now() - this.temporaryMessage.startTime
      if (elapsed >= this.temporaryMessage.duration) {
        this.temporaryMessage = null
      }
    }
  }
}

module.exports = MessageManager
