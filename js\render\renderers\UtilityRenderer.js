/**
 * 渲染工具类
 * 提供通用的绘制工具方法，供其他渲染器使用
 */
class UtilityRenderer {
  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  static drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  /**
   * 绘制星星
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 大小
   * @param {number} points - 星星的角数
   */
  static drawStar(ctx, x, y, size, points) {
    ctx.save()
    ctx.translate(x, y)
    ctx.beginPath()
    
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points
      const radius = i % 2 === 0 ? size : size * 0.5
      const px = Math.cos(angle) * radius
      const py = Math.sin(angle) * radius
      
      if (i === 0) {
        ctx.moveTo(px, py)
      } else {
        ctx.lineTo(px, py)
      }
    }
    
    ctx.closePath()
    ctx.fill()
    ctx.restore()
  }

  /**
   * 绘制增强版星星评价
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} y - Y坐标
   * @param {number} starCount - 星星数量
   */
  static drawEnhancedStars(ctx, centerX, y, starCount) {
    const starSize = 10
    const spacing = 18
    const totalWidth = 3 * starSize + 2 * spacing
    const startX = centerX - totalWidth / 2

    ctx.save()
    for (let i = 0; i < 3; i++) {
      const x = startX + i * spacing
      if (i < starCount) {
        // 金色星星
        ctx.fillStyle = '#FFD700'
        ctx.strokeStyle = '#FFA500'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      } else {
        // 灰色星星
        ctx.fillStyle = '#DDDDDD'
        ctx.strokeStyle = '#CCCCCC'
        ctx.lineWidth = 1
        this.drawStar(ctx, x, y, starSize, 5)
        ctx.fill()
        ctx.stroke()
      }
    }
    ctx.restore()
  }

  /**
   * 计算关卡星级评价
   * @param {Object} record - 关卡记录
   * @returns {number} 星级数量 (1-3)
   */
  static calculateStars(record) {
    // 简单的星级评价算法
    if (record.moves <= 20) return 3
    if (record.moves <= 35) return 2
    return 1
  }
}

// CommonJS导出
module.exports = UtilityRenderer
