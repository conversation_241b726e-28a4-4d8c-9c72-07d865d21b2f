/**
 * 调试渲染器
 * 负责调试信息的渲染，包括方块层级、可点击状态、统计信息等
 */
class DebugRenderer {
  /**
   * 构造函数
   * @param {Object} main - 主游戏实例
   */
  constructor(main) {
    this.main = main
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    if (!this.main.board) return

    const ctx = this.main.ctx
    ctx.save()

    // 渲染方块层级数字
    this.renderBlockLayerInfo(ctx)

    // 显示调试信息面板
    this.renderDebugPanel(ctx)

    ctx.restore()
  }

  /**
   * 渲染方块层级信息
   */
  renderBlockLayerInfo(ctx) {
    this.main.board.blocks.forEach(block => {
      if (block.isVisible) {
        // 绘制层级数字背景
        ctx.fillStyle = block.isClickable ? 'rgba(0, 255, 0, 0.8)' : 'rgba(255, 0, 0, 0.8)'
        ctx.fillRect(block.x - 2, block.y - 2, 16, 16)

        // 绘制层级数字
        ctx.fillStyle = '#FFFFFF'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(block.layer.toString(), block.x + 6, block.y + 10)

        // 如果不可点击，在方块上绘制红色X
        if (!block.isClickable) {
          ctx.strokeStyle = '#FF0000'
          ctx.lineWidth = 3
          ctx.beginPath()
          ctx.moveTo(block.x + 5, block.y + 5)
          ctx.lineTo(block.x + block.width - 5, block.y + block.height - 5)
          ctx.moveTo(block.x + block.width - 5, block.y + 5)
          ctx.lineTo(block.x + 5, block.y + block.height - 5)
          ctx.stroke()
        }
      }
    })
  }

  /**
   * 渲染调试信息面板
   */
  renderDebugPanel(ctx) {
    const debugPanelY = this.main.canvasSize.screenHeight - 160
    
    // 绘制调试面板背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.fillRect(0, debugPanelY, this.main.canvasSize.screenWidth, 160)

    // 设置文字样式
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '14px Arial'
    ctx.textAlign = 'left'

    // 计算统计信息
    const totalBlocks = this.main.board.blocks.length
    const visibleBlocks = this.main.board.blocks.filter(b => b.isVisible).length
    const clickableBlocks = this.main.board.blocks.filter(b => b.isVisible && b.isClickable).length

    // 显示基本统计信息
    ctx.fillText(`调试信息:`, 10, debugPanelY + 20)
    ctx.fillText(`总方块数: ${totalBlocks}`, 10, debugPanelY + 40)
    ctx.fillText(`可见方块: ${visibleBlocks}`, 10, debugPanelY + 60)
    ctx.fillText(`可点击方块: ${clickableBlocks}`, 10, debugPanelY + 80)

    // 显示层级分布
    this.renderLayerDistribution(ctx, debugPanelY)
  }

  /**
   * 渲染层级分布信息
   */
  renderLayerDistribution(ctx, debugPanelY) {
    const layerStats = new Map()
    this.main.board.blocks.forEach(block => {
      if (block.isVisible) {
        const count = layerStats.get(block.layer) || 0
        layerStats.set(block.layer, count + 1)
      }
    })

    let y = debugPanelY + 100
    ctx.fillText(`层级分布:`, 10, y)
    y += 20
    
    layerStats.forEach((count, layer) => {
      ctx.fillText(`层级${layer}: ${count}个方块`, 10, y)
      y += 15
    })
  }
}

// CommonJS导出
module.exports = DebugRenderer
